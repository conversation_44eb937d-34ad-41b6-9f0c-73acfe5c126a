<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>Summary - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1>Summary<a class="button" href="https://github.com/danielpalme/ReportGenerator" title="Star on GitHub"><i class="icon-star"></i>Star</a><a class="button" href="https://github.com/sponsors/danielpalme" title="Become a sponsor"><i class="icon-sponsor"></i>Sponsor</a></h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Parser:</th>
<td class="limit-width " title="MultiReport (3x Cobertura)">MultiReport (3x Cobertura)</td>
</tr>
<tr>
<th>Assemblies:</th>
<td class="limit-width right" title="1">1</td>
</tr>
<tr>
<th>Classes:</th>
<td class="limit-width right" title="10">10</td>
</tr>
<tr>
<th>Files:</th>
<td class="limit-width right" title="10">10</td>
</tr>
<tr>
<th>Coverage date:</th>
<td class="limit-width " title="26/07/2025 - 1:06:23 SA - 26/07/2025 - 1:23:59 SA">26/07/2025 - 1:06:23 SA - 26/07/2025 - 1:23:59 SA</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar32">67%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="160">160</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="76">76</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="236">236</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="455">455</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="160 of 236">67.7%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar20">79%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="35">35</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="44">44</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="35 of 44">79.5%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Risk Hotspots</h1>
<risk-hotspots>
</risk-hotspots>
<p>No risk hotspots found.</p>
<h1>Coverage</h1>
<coverage-info>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column90" />
<col class="column105" />
<col class="column100" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
<col class="column90" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
</colgroup>
<thead>
<tr class="header"><th></th><th colspan="6" class="center">Line coverage</th><th colspan="4" class="center">Branch coverage</th></tr>
<tr><th>Name</th><th class="right">Covered</th><th class="right">Uncovered</th><th class="right">Coverable</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th><th class="right">Covered</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th></tr></thead>
<tbody>
<tr><th>UserProfileApi</th><th class="right">160</th><th class="right">76</th><th class="right">236</th><th class="right">455</th><th title="160/236" class="right">67.7%</th><th><table class="coverage"><tr><td class="green covered68">&nbsp;</td><td class="red covered32">&nbsp;</td></tr></table></th><th class="right">35</th><th class="right">44</th><th class="right" title="35/44">79.5%</th><th><table class="coverage"><tr><td class="green covered80">&nbsp;</td><td class="red covered20">&nbsp;</td></tr></table></th></tr>
<tr><td><a href="UserProfileApi_Program.html">Program</a></td><td class="right">0</td><td class="right">20</td><td class="right">20</td><td class="right">43</td><td title="0/20" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">2</td><td class="right" title="0/2">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_UsersController.html">UserProfileApi.Controllers.UsersController</a></td><td class="right">44</td><td class="right">0</td><td class="right">44</td><td class="right">99</td><td title="44/44" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">2</td><td class="right">2</td><td class="right" title="2/2">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_UserDto.html">UserProfileApi.DTOs.UserDto</a></td><td class="right">8</td><td class="right">0</td><td class="right">8</td><td class="right">14</td><td title="8/8" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_AppException.html">UserProfileApi.Exceptions.AppException</a></td><td class="right">3</td><td class="right">3</td><td class="right">6</td><td class="right">13</td><td title="3/6" class="right">50%</td><td><table class="coverage"><tr><td class="green covered50">&nbsp;</td><td class="red covered50">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_UserProfile.html">UserProfileApi.Mappings.UserProfile</a></td><td class="right">10</td><td class="right">0</td><td class="right">10</td><td class="right">21</td><td title="10/10" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_User.html">UserProfileApi.Models.User</a></td><td class="right">9</td><td class="right">0</td><td class="right">9</td><td class="right">27</td><td title="9/9" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_UserRepository.html">UserProfileApi.Repositories.UserRepository</a></td><td class="right">0</td><td class="right">53</td><td class="right">53</td><td class="right">72</td><td title="0/53" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_AuthUtil.html">UserProfileApi.Services.AuthUtil</a></td><td class="right">13</td><td class="right">0</td><td class="right">13</td><td class="right">32</td><td title="13/13" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">6</td><td class="right">6</td><td class="right" title="6/6">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_CloudinaryService.html">UserProfileApi.Services.CloudinaryService</a></td><td class="right">12</td><td class="right">0</td><td class="right">12</td><td class="right">30</td><td title="12/12" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">8</td><td class="right">8</td><td class="right" title="8/8">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="UserProfileApi_UserService.html">UserProfileApi.Services.UserService</a></td><td class="right">61</td><td class="right">0</td><td class="right">61</td><td class="right">104</td><td title="61/61" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">19</td><td class="right">20</td><td class="right" title="19/20">95%</td><td><table class="coverage"><tr><td class="green covered95">&nbsp;</td><td class="red covered5">&nbsp;</td></tr></table></td></tr>
</tbody>
</table>
</div>
</coverage-info>
<div class="footer">Generated by: ReportGenerator ********<br />26/07/2025 - 1:25:50 SA<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'main.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script>
</body></html>