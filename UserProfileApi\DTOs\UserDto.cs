namespace UserProfileApi.DTOs
{
    public class UserDto
    {
        public long? Id { get; set; }
        public string? Fullname { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? ImageUrl { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public IFormFile? Img { get; set; } // For file upload
    }
}
