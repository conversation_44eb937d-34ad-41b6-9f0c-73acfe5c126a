using UserProfileApi.Exceptions;

namespace UserProfileApi.Services
{
    public class CloudinaryService : ICloudinaryService
    {
        // Mock implementation - in real app, you would use Cloudinary SDK
        public async Task<string> UploadImageAsync(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new AppException("File không hợp lệ");

            // Validate file type
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
            if (!allowedTypes.Contains(file.ContentType.ToLower()))
                throw new AppException("Chỉ chấp nhận file ảnh (JPEG, PNG, GIF)");

            // Validate file size (max 5MB)
            if (file.Length > 5 * 1024 * 1024)
                throw new AppException("File ảnh không được vượt quá 5MB");

            // Mock upload - in real implementation, upload to Cloudinary
            await Task.Delay(100); // Simulate upload time
            
            // Return mock URL
            var fileName = Guid.NewGuid().ToString() + Path.GetExtension(file.FileName);
            return $"https://res.cloudinary.com/demo/image/upload/v1/{fileName}";
        }
    }
}
