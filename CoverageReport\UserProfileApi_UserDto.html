<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>UserProfileApi.DTOs.UserDto - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1><a href="index.html" class="back">&lt;</a> Summary</h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Class:</th>
<td class="limit-width " title="UserProfileApi.DTOs.UserDto">UserProfileApi.DTOs.UserDto</td>
</tr>
<tr>
<th>Assembly:</th>
<td class="limit-width " title="UserProfileApi">UserProfileApi</td>
</tr>
<tr>
<th>File(s):</th>
<td class="overflow-wrap"><a href="#DASEM5SWTTestingCUserProfileApiDTOsUserDtocs" class="navigatetohash">D:\ASEM5\SWT\TestingC\UserProfileApi\DTOs\UserDto.cs</a></td>
</tr>
</table>
</div>
</div>
</div>
</div>
<div class="card-group">
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar0">100%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="8">8</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="8">8</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="14">14</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="8 of 8">100%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large">N/A</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="N/A">N/A</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Metrics</h1>
<div class="table-responsive">
<table class="overview table-fixed">
<thead><tr><th>Method</th><th>Branch coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th><th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th><th>Line coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th></tr></thead>
<tbody>
<tr><td title="get_Id()"><a href="#file0_line5" class="navigatetohash">get_Id()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Fullname()"><a href="#file0_line6" class="navigatetohash">get_Fullname()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Email()"><a href="#file0_line7" class="navigatetohash">get_Email()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Phone()"><a href="#file0_line8" class="navigatetohash">get_Phone()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Address()"><a href="#file0_line9" class="navigatetohash">get_Address()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_ImageUrl()"><a href="#file0_line10" class="navigatetohash">get_ImageUrl()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_DateOfBirth()"><a href="#file0_line11" class="navigatetohash">get_DateOfBirth()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Img()"><a href="#file0_line12" class="navigatetohash">get_Img()</a></td><td>100%</td><td>1</td><td>100%</td></tr>
</tbody>
</table>
</div>
<h1>File(s)</h1>
<h2 id="DASEM5SWTTestingCUserProfileApiDTOsUserDtocs">D:\ASEM5\SWT\TestingC\UserProfileApi\DTOs\UserDto.cs</h2>
<div class="table-responsive">
<table class="lineAnalysis">
<thead><tr><th></th><th>#</th><th>Line</th><th></th><th>Line coverage</th></tr></thead>
<tbody>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line1"></a><code>1</code></td><td></td><td class="lightgray"><code>namespace&nbsp;UserProfileApi.DTOs</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line2"></a><code>2</code></td><td></td><td class="lightgray"><code>{</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line3"></a><code>3</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;class&nbsp;UserDto</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line4"></a><code>4</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Covered (33 visits)" data-coverage="{'AllTestMethods': {'VC': '33', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">33</td><td class="rightmargin right"><a id="file0_line5"></a><code>5</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;long?&nbsp;Id&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (122 visits)" data-coverage="{'AllTestMethods': {'VC': '122', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">122</td><td class="rightmargin right"><a id="file0_line6"></a><code>6</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;Fullname&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (46 visits)" data-coverage="{'AllTestMethods': {'VC': '46', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">46</td><td class="rightmargin right"><a id="file0_line7"></a><code>7</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;Email&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (97 visits)" data-coverage="{'AllTestMethods': {'VC': '97', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">97</td><td class="rightmargin right"><a id="file0_line8"></a><code>8</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;Phone&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (84 visits)" data-coverage="{'AllTestMethods': {'VC': '84', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">84</td><td class="rightmargin right"><a id="file0_line9"></a><code>9</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;Address&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (64 visits)" data-coverage="{'AllTestMethods': {'VC': '64', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">64</td><td class="rightmargin right"><a id="file0_line10"></a><code>10</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string?&nbsp;ImageUrl&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (45 visits)" data-coverage="{'AllTestMethods': {'VC': '45', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">45</td><td class="rightmargin right"><a id="file0_line11"></a><code>11</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;DateTime?&nbsp;DateOfBirth&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}</code></td></tr>
<tr class="coverableline" title="Covered (33 visits)" data-coverage="{'AllTestMethods': {'VC': '33', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">33</td><td class="rightmargin right"><a id="file0_line12"></a><code>12</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;IFormFile?&nbsp;Img&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;//&nbsp;For&nbsp;file&nbsp;upload</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line13"></a><code>13</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line14"></a><code>14</code></td><td></td><td class="lightgray"><code>}</code></td></tr>
</tbody>
</table>
</div>
<div class="footer">Generated by: ReportGenerator 5.1.26.0<br />26/07/2025 - 1:25:50 SA<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div>
<div class="containerright">
<div class="containerrightfixed">
<h1>Methods/Properties</h1>
<a href="#file0_line5" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Id()"><i class="icon-wrench"></i>get_Id()</a><br />
<a href="#file0_line6" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Fullname()"><i class="icon-wrench"></i>get_Fullname()</a><br />
<a href="#file0_line7" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Email()"><i class="icon-wrench"></i>get_Email()</a><br />
<a href="#file0_line8" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Phone()"><i class="icon-wrench"></i>get_Phone()</a><br />
<a href="#file0_line9" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Address()"><i class="icon-wrench"></i>get_Address()</a><br />
<a href="#file0_line10" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_ImageUrl()"><i class="icon-wrench"></i>get_ImageUrl()</a><br />
<a href="#file0_line11" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_DateOfBirth()"><i class="icon-wrench"></i>get_DateOfBirth()</a><br />
<a href="#file0_line12" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Img()"><i class="icon-wrench"></i>get_Img()</a><br />
<br/></div>
</div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'class.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script>
</body></html>