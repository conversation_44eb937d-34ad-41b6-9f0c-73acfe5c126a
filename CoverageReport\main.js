/* Chartist.js 0.11.4
 * Copyright © 2019 Gion Kunz
 * Free to use under either the WTFPL license or the MIT license.
 * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-WTFPL
 * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-MIT
 */

!function (a, b) { "function" == typeof define && define.amd ? define("Chartist", [], function () { return a.Chartist = b() }) : "object" == typeof module && module.exports ? module.exports = b() : a.Chartist = b() }(this, function () {
    var a = { version: "0.11.4" }; return function (a, b) { "use strict"; var c = a.window, d = a.document; b.namespaces = { svg: "http://www.w3.org/2000/svg", xmlns: "http://www.w3.org/2000/xmlns/", xhtml: "http://www.w3.org/1999/xhtml", xlink: "http://www.w3.org/1999/xlink", ct: "http://gionkunz.github.com/chartist-js/ct" }, b.noop = function (a) { return a }, b.alphaNumerate = function (a) { return String.fromCharCode(97 + a % 26) }, b.extend = function (a) { var c, d, e; for (a = a || {}, c = 1; c < arguments.length; c++) { d = arguments[c]; for (var f in d) e = d[f], "object" != typeof e || null === e || e instanceof Array ? a[f] = e : a[f] = b.extend(a[f], e) } return a }, b.replaceAll = function (a, b, c) { return a.replace(new RegExp(b, "g"), c) }, b.ensureUnit = function (a, b) { return "number" == typeof a && (a += b), a }, b.quantity = function (a) { if ("string" == typeof a) { var b = /^(\d+)\s*(.*)$/g.exec(a); return { value: +b[1], unit: b[2] || void 0 } } return { value: a } }, b.querySelector = function (a) { return a instanceof Node ? a : d.querySelector(a) }, b.times = function (a) { return Array.apply(null, new Array(a)) }, b.sum = function (a, b) { return a + (b ? b : 0) }, b.mapMultiply = function (a) { return function (b) { return b * a } }, b.mapAdd = function (a) { return function (b) { return b + a } }, b.serialMap = function (a, c) { var d = [], e = Math.max.apply(null, a.map(function (a) { return a.length })); return b.times(e).forEach(function (b, e) { var f = a.map(function (a) { return a[e] }); d[e] = c.apply(null, f) }), d }, b.roundWithPrecision = function (a, c) { var d = Math.pow(10, c || b.precision); return Math.round(a * d) / d }, b.precision = 8, b.escapingMap = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#039;" }, b.serialize = function (a) { return null === a || void 0 === a ? a : ("number" == typeof a ? a = "" + a : "object" == typeof a && (a = JSON.stringify({ data: a })), Object.keys(b.escapingMap).reduce(function (a, c) { return b.replaceAll(a, c, b.escapingMap[c]) }, a)) }, b.deserialize = function (a) { if ("string" != typeof a) return a; a = Object.keys(b.escapingMap).reduce(function (a, c) { return b.replaceAll(a, b.escapingMap[c], c) }, a); try { a = JSON.parse(a), a = void 0 !== a.data ? a.data : a } catch (c) { } return a }, b.createSvg = function (a, c, d, e) { var f; return c = c || "100%", d = d || "100%", Array.prototype.slice.call(a.querySelectorAll("svg")).filter(function (a) { return a.getAttributeNS(b.namespaces.xmlns, "ct") }).forEach(function (b) { a.removeChild(b) }), f = new b.Svg("svg").attr({ width: c, height: d }).addClass(e), f._node.style.width = c, f._node.style.height = d, a.appendChild(f._node), f }, b.normalizeData = function (a, c, d) { var e, f = { raw: a, normalized: {} }; return f.normalized.series = b.getDataArray({ series: a.series || [] }, c, d), e = f.normalized.series.every(function (a) { return a instanceof Array }) ? Math.max.apply(null, f.normalized.series.map(function (a) { return a.length })) : f.normalized.series.length, f.normalized.labels = (a.labels || []).slice(), Array.prototype.push.apply(f.normalized.labels, b.times(Math.max(0, e - f.normalized.labels.length)).map(function () { return "" })), c && b.reverseData(f.normalized), f }, b.safeHasProperty = function (a, b) { return null !== a && "object" == typeof a && a.hasOwnProperty(b) }, b.isDataHoleValue = function (a) { return null === a || void 0 === a || "number" == typeof a && isNaN(a) }, b.reverseData = function (a) { a.labels.reverse(), a.series.reverse(); for (var b = 0; b < a.series.length; b++)"object" == typeof a.series[b] && void 0 !== a.series[b].data ? a.series[b].data.reverse() : a.series[b] instanceof Array && a.series[b].reverse() }, b.getDataArray = function (a, c, d) { function e(a) { if (b.safeHasProperty(a, "value")) return e(a.value); if (b.safeHasProperty(a, "data")) return e(a.data); if (a instanceof Array) return a.map(e); if (!b.isDataHoleValue(a)) { if (d) { var c = {}; return "string" == typeof d ? c[d] = b.getNumberOrUndefined(a) : c.y = b.getNumberOrUndefined(a), c.x = a.hasOwnProperty("x") ? b.getNumberOrUndefined(a.x) : c.x, c.y = a.hasOwnProperty("y") ? b.getNumberOrUndefined(a.y) : c.y, c } return b.getNumberOrUndefined(a) } } return a.series.map(e) }, b.normalizePadding = function (a, b) { return b = b || 0, "number" == typeof a ? { top: a, right: a, bottom: a, left: a } : { top: "number" == typeof a.top ? a.top : b, right: "number" == typeof a.right ? a.right : b, bottom: "number" == typeof a.bottom ? a.bottom : b, left: "number" == typeof a.left ? a.left : b } }, b.getMetaData = function (a, b) { var c = a.data ? a.data[b] : a[b]; return c ? c.meta : void 0 }, b.orderOfMagnitude = function (a) { return Math.floor(Math.log(Math.abs(a)) / Math.LN10) }, b.projectLength = function (a, b, c) { return b / c.range * a }, b.getAvailableHeight = function (a, c) { return Math.max((b.quantity(c.height).value || a.height()) - (c.chartPadding.top + c.chartPadding.bottom) - c.axisX.offset, 0) }, b.getHighLow = function (a, c, d) { function e(a) { if (void 0 !== a) if (a instanceof Array) for (var b = 0; b < a.length; b++)e(a[b]); else { var c = d ? +a[d] : +a; g && c > f.high && (f.high = c), h && c < f.low && (f.low = c) } } c = b.extend({}, c, d ? c["axis" + d.toUpperCase()] : {}); var f = { high: void 0 === c.high ? -Number.MAX_VALUE : +c.high, low: void 0 === c.low ? Number.MAX_VALUE : +c.low }, g = void 0 === c.high, h = void 0 === c.low; return (g || h) && e(a), (c.referenceValue || 0 === c.referenceValue) && (f.high = Math.max(c.referenceValue, f.high), f.low = Math.min(c.referenceValue, f.low)), f.high <= f.low && (0 === f.low ? f.high = 1 : f.low < 0 ? f.high = 0 : f.high > 0 ? f.low = 0 : (f.high = 1, f.low = 0)), f }, b.isNumeric = function (a) { return null !== a && isFinite(a) }, b.isFalseyButZero = function (a) { return !a && 0 !== a }, b.getNumberOrUndefined = function (a) { return b.isNumeric(a) ? +a : void 0 }, b.isMultiValue = function (a) { return "object" == typeof a && ("x" in a || "y" in a) }, b.getMultiValue = function (a, c) { return b.isMultiValue(a) ? b.getNumberOrUndefined(a[c || "y"]) : b.getNumberOrUndefined(a) }, b.rho = function (a) { function b(a, c) { return a % c === 0 ? c : b(c, a % c) } function c(a) { return a * a + 1 } if (1 === a) return a; var d, e = 2, f = 2; if (a % 2 === 0) return 2; do e = c(e) % a, f = c(c(f)) % a, d = b(Math.abs(e - f), a); while (1 === d); return d }, b.getBounds = function (a, c, d, e) { function f(a, b) { return a === (a += b) && (a *= 1 + (b > 0 ? o : -o)), a } var g, h, i, j = 0, k = { high: c.high, low: c.low }; k.valueRange = k.high - k.low, k.oom = b.orderOfMagnitude(k.valueRange), k.step = Math.pow(10, k.oom), k.min = Math.floor(k.low / k.step) * k.step, k.max = Math.ceil(k.high / k.step) * k.step, k.range = k.max - k.min, k.numberOfSteps = Math.round(k.range / k.step); var l = b.projectLength(a, k.step, k), m = l < d, n = e ? b.rho(k.range) : 0; if (e && b.projectLength(a, 1, k) >= d) k.step = 1; else if (e && n < k.step && b.projectLength(a, n, k) >= d) k.step = n; else for (; ;) { if (m && b.projectLength(a, k.step, k) <= d) k.step *= 2; else { if (m || !(b.projectLength(a, k.step / 2, k) >= d)) break; if (k.step /= 2, e && k.step % 1 !== 0) { k.step *= 2; break } } if (j++ > 1e3) throw new Error("Exceeded maximum number of iterations while optimizing scale step!") } var o = 2.221e-16; for (k.step = Math.max(k.step, o), h = k.min, i = k.max; h + k.step <= k.low;)h = f(h, k.step); for (; i - k.step >= k.high;)i = f(i, -k.step); k.min = h, k.max = i, k.range = k.max - k.min; var p = []; for (g = k.min; g <= k.max; g = f(g, k.step)) { var q = b.roundWithPrecision(g); q !== p[p.length - 1] && p.push(q) } return k.values = p, k }, b.polarToCartesian = function (a, b, c, d) { var e = (d - 90) * Math.PI / 180; return { x: a + c * Math.cos(e), y: b + c * Math.sin(e) } }, b.createChartRect = function (a, c, d) { var e = !(!c.axisX && !c.axisY), f = e ? c.axisY.offset : 0, g = e ? c.axisX.offset : 0, h = a.width() || b.quantity(c.width).value || 0, i = a.height() || b.quantity(c.height).value || 0, j = b.normalizePadding(c.chartPadding, d); h = Math.max(h, f + j.left + j.right), i = Math.max(i, g + j.top + j.bottom); var k = { padding: j, width: function () { return this.x2 - this.x1 }, height: function () { return this.y1 - this.y2 } }; return e ? ("start" === c.axisX.position ? (k.y2 = j.top + g, k.y1 = Math.max(i - j.bottom, k.y2 + 1)) : (k.y2 = j.top, k.y1 = Math.max(i - j.bottom - g, k.y2 + 1)), "start" === c.axisY.position ? (k.x1 = j.left + f, k.x2 = Math.max(h - j.right, k.x1 + 1)) : (k.x1 = j.left, k.x2 = Math.max(h - j.right - f, k.x1 + 1))) : (k.x1 = j.left, k.x2 = Math.max(h - j.right, k.x1 + 1), k.y2 = j.top, k.y1 = Math.max(i - j.bottom, k.y2 + 1)), k }, b.createGrid = function (a, c, d, e, f, g, h, i) { var j = {}; j[d.units.pos + "1"] = a, j[d.units.pos + "2"] = a, j[d.counterUnits.pos + "1"] = e, j[d.counterUnits.pos + "2"] = e + f; var k = g.elem("line", j, h.join(" ")); i.emit("draw", b.extend({ type: "grid", axis: d, index: c, group: g, element: k }, j)) }, b.createGridBackground = function (a, b, c, d) { var e = a.elem("rect", { x: b.x1, y: b.y2, width: b.width(), height: b.height() }, c, !0); d.emit("draw", { type: "gridBackground", group: a, element: e }) }, b.createLabel = function (a, c, e, f, g, h, i, j, k, l, m) { var n, o = {}; if (o[g.units.pos] = a + i[g.units.pos], o[g.counterUnits.pos] = i[g.counterUnits.pos], o[g.units.len] = c, o[g.counterUnits.len] = Math.max(0, h - 10), l) { var p = d.createElement("span"); p.className = k.join(" "), p.setAttribute("xmlns", b.namespaces.xhtml), p.innerText = f[e], p.style[g.units.len] = Math.round(o[g.units.len]) + "px", p.style[g.counterUnits.len] = Math.round(o[g.counterUnits.len]) + "px", n = j.foreignObject(p, b.extend({ style: "overflow: visible;" }, o)) } else n = j.elem("text", o, k.join(" ")).text(f[e]); m.emit("draw", b.extend({ type: "label", axis: g, index: e, group: j, element: n, text: f[e] }, o)) }, b.getSeriesOption = function (a, b, c) { if (a.name && b.series && b.series[a.name]) { var d = b.series[a.name]; return d.hasOwnProperty(c) ? d[c] : b[c] } return b[c] }, b.optionsProvider = function (a, d, e) { function f(a) { var f = h; if (h = b.extend({}, j), d) for (i = 0; i < d.length; i++) { var g = c.matchMedia(d[i][0]); g.matches && (h = b.extend(h, d[i][1])) } e && a && e.emit("optionsChanged", { previousOptions: f, currentOptions: h }) } function g() { k.forEach(function (a) { a.removeListener(f) }) } var h, i, j = b.extend({}, a), k = []; if (!c.matchMedia) throw "window.matchMedia not found! Make sure you're using a polyfill."; if (d) for (i = 0; i < d.length; i++) { var l = c.matchMedia(d[i][0]); l.addListener(f), k.push(l) } return f(), { removeMediaQueryListeners: g, getCurrentOptions: function () { return b.extend({}, h) } } }, b.splitIntoSegments = function (a, c, d) { var e = { increasingX: !1, fillHoles: !1 }; d = b.extend({}, e, d); for (var f = [], g = !0, h = 0; h < a.length; h += 2)void 0 === b.getMultiValue(c[h / 2].value) ? d.fillHoles || (g = !0) : (d.increasingX && h >= 2 && a[h] <= a[h - 2] && (g = !0), g && (f.push({ pathCoordinates: [], valueData: [] }), g = !1), f[f.length - 1].pathCoordinates.push(a[h], a[h + 1]), f[f.length - 1].valueData.push(c[h / 2])); return f } }(this || global, a), function (a, b) { "use strict"; b.Interpolation = {}, b.Interpolation.none = function (a) { var c = { fillHoles: !1 }; return a = b.extend({}, c, a), function (c, d) { for (var e = new b.Svg.Path, f = !0, g = 0; g < c.length; g += 2) { var h = c[g], i = c[g + 1], j = d[g / 2]; void 0 !== b.getMultiValue(j.value) ? (f ? e.move(h, i, !1, j) : e.line(h, i, !1, j), f = !1) : a.fillHoles || (f = !0) } return e } }, b.Interpolation.simple = function (a) { var c = { divisor: 2, fillHoles: !1 }; a = b.extend({}, c, a); var d = 1 / Math.max(1, a.divisor); return function (c, e) { for (var f, g, h, i = new b.Svg.Path, j = 0; j < c.length; j += 2) { var k = c[j], l = c[j + 1], m = (k - f) * d, n = e[j / 2]; void 0 !== n.value ? (void 0 === h ? i.move(k, l, !1, n) : i.curve(f + m, g, k - m, l, k, l, !1, n), f = k, g = l, h = n) : a.fillHoles || (f = k = h = void 0) } return i } }, b.Interpolation.cardinal = function (a) { var c = { tension: 1, fillHoles: !1 }; a = b.extend({}, c, a); var d = Math.min(1, Math.max(0, a.tension)), e = 1 - d; return function f(c, g) { var h = b.splitIntoSegments(c, g, { fillHoles: a.fillHoles }); if (h.length) { if (h.length > 1) { var i = []; return h.forEach(function (a) { i.push(f(a.pathCoordinates, a.valueData)) }), b.Svg.Path.join(i) } if (c = h[0].pathCoordinates, g = h[0].valueData, c.length <= 4) return b.Interpolation.none()(c, g); for (var j, k = (new b.Svg.Path).move(c[0], c[1], !1, g[0]), l = 0, m = c.length; m - 2 * !j > l; l += 2) { var n = [{ x: +c[l - 2], y: +c[l - 1] }, { x: +c[l], y: +c[l + 1] }, { x: +c[l + 2], y: +c[l + 3] }, { x: +c[l + 4], y: +c[l + 5] }]; j ? l ? m - 4 === l ? n[3] = { x: +c[0], y: +c[1] } : m - 2 === l && (n[2] = { x: +c[0], y: +c[1] }, n[3] = { x: +c[2], y: +c[3] }) : n[0] = { x: +c[m - 2], y: +c[m - 1] } : m - 4 === l ? n[3] = n[2] : l || (n[0] = { x: +c[l], y: +c[l + 1] }), k.curve(d * (-n[0].x + 6 * n[1].x + n[2].x) / 6 + e * n[2].x, d * (-n[0].y + 6 * n[1].y + n[2].y) / 6 + e * n[2].y, d * (n[1].x + 6 * n[2].x - n[3].x) / 6 + e * n[2].x, d * (n[1].y + 6 * n[2].y - n[3].y) / 6 + e * n[2].y, n[2].x, n[2].y, !1, g[(l + 2) / 2]) } return k } return b.Interpolation.none()([]) } }, b.Interpolation.monotoneCubic = function (a) { var c = { fillHoles: !1 }; return a = b.extend({}, c, a), function d(c, e) { var f = b.splitIntoSegments(c, e, { fillHoles: a.fillHoles, increasingX: !0 }); if (f.length) { if (f.length > 1) { var g = []; return f.forEach(function (a) { g.push(d(a.pathCoordinates, a.valueData)) }), b.Svg.Path.join(g) } if (c = f[0].pathCoordinates, e = f[0].valueData, c.length <= 4) return b.Interpolation.none()(c, e); var h, i, j = [], k = [], l = c.length / 2, m = [], n = [], o = [], p = []; for (h = 0; h < l; h++)j[h] = c[2 * h], k[h] = c[2 * h + 1]; for (h = 0; h < l - 1; h++)o[h] = k[h + 1] - k[h], p[h] = j[h + 1] - j[h], n[h] = o[h] / p[h]; for (m[0] = n[0], m[l - 1] = n[l - 2], h = 1; h < l - 1; h++)0 === n[h] || 0 === n[h - 1] || n[h - 1] > 0 != n[h] > 0 ? m[h] = 0 : (m[h] = 3 * (p[h - 1] + p[h]) / ((2 * p[h] + p[h - 1]) / n[h - 1] + (p[h] + 2 * p[h - 1]) / n[h]), isFinite(m[h]) || (m[h] = 0)); for (i = (new b.Svg.Path).move(j[0], k[0], !1, e[0]), h = 0; h < l - 1; h++)i.curve(j[h] + p[h] / 3, k[h] + m[h] * p[h] / 3, j[h + 1] - p[h] / 3, k[h + 1] - m[h + 1] * p[h] / 3, j[h + 1], k[h + 1], !1, e[h + 1]); return i } return b.Interpolation.none()([]) } }, b.Interpolation.step = function (a) { var c = { postpone: !0, fillHoles: !1 }; return a = b.extend({}, c, a), function (c, d) { for (var e, f, g, h = new b.Svg.Path, i = 0; i < c.length; i += 2) { var j = c[i], k = c[i + 1], l = d[i / 2]; void 0 !== l.value ? (void 0 === g ? h.move(j, k, !1, l) : (a.postpone ? h.line(j, f, !1, g) : h.line(e, k, !1, l), h.line(j, k, !1, l)), e = j, f = k, g = l) : a.fillHoles || (e = f = g = void 0) } return h } } }(this || global, a), function (a, b) { "use strict"; b.EventEmitter = function () { function a(a, b) { d[a] = d[a] || [], d[a].push(b) } function b(a, b) { d[a] && (b ? (d[a].splice(d[a].indexOf(b), 1), 0 === d[a].length && delete d[a]) : delete d[a]) } function c(a, b) { d[a] && d[a].forEach(function (a) { a(b) }), d["*"] && d["*"].forEach(function (c) { c(a, b) }) } var d = []; return { addEventHandler: a, removeEventHandler: b, emit: c } } }(this || global, a), function (a, b) { "use strict"; function c(a) { var b = []; if (a.length) for (var c = 0; c < a.length; c++)b.push(a[c]); return b } function d(a, c) { var d = c || this.prototype || b.Class, e = Object.create(d); b.Class.cloneDefinitions(e, a); var f = function () { var a, c = e.constructor || function () { }; return a = this === b ? Object.create(e) : this, c.apply(a, Array.prototype.slice.call(arguments, 0)), a }; return f.prototype = e, f["super"] = d, f.extend = this.extend, f } function e() { var a = c(arguments), b = a[0]; return a.splice(1, a.length - 1).forEach(function (a) { Object.getOwnPropertyNames(a).forEach(function (c) { delete b[c], Object.defineProperty(b, c, Object.getOwnPropertyDescriptor(a, c)) }) }), b } b.Class = { extend: d, cloneDefinitions: e } }(this || global, a), function (a, b) { "use strict"; function c(a, c, d) { return a && (this.data = a || {}, this.data.labels = this.data.labels || [], this.data.series = this.data.series || [], this.eventEmitter.emit("data", { type: "update", data: this.data })), c && (this.options = b.extend({}, d ? this.options : this.defaultOptions, c), this.initializeTimeoutId || (this.optionsProvider.removeMediaQueryListeners(), this.optionsProvider = b.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter))), this.initializeTimeoutId || this.createChart(this.optionsProvider.getCurrentOptions()), this } function d() { return this.initializeTimeoutId ? i.clearTimeout(this.initializeTimeoutId) : (i.removeEventListener("resize", this.resizeListener), this.optionsProvider.removeMediaQueryListeners()), this } function e(a, b) { return this.eventEmitter.addEventHandler(a, b), this } function f(a, b) { return this.eventEmitter.removeEventHandler(a, b), this } function g() { i.addEventListener("resize", this.resizeListener), this.optionsProvider = b.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter), this.eventEmitter.addEventHandler("optionsChanged", function () { this.update() }.bind(this)), this.options.plugins && this.options.plugins.forEach(function (a) { a instanceof Array ? a[0](this, a[1]) : a(this) }.bind(this)), this.eventEmitter.emit("data", { type: "initial", data: this.data }), this.createChart(this.optionsProvider.getCurrentOptions()), this.initializeTimeoutId = void 0 } function h(a, c, d, e, f) { this.container = b.querySelector(a), this.data = c || {}, this.data.labels = this.data.labels || [], this.data.series = this.data.series || [], this.defaultOptions = d, this.options = e, this.responsiveOptions = f, this.eventEmitter = b.EventEmitter(), this.supportsForeignObject = b.Svg.isSupported("Extensibility"), this.supportsAnimations = b.Svg.isSupported("AnimationEventsAttribute"), this.resizeListener = function () { this.update() }.bind(this), this.container && (this.container.__chartist__ && this.container.__chartist__.detach(), this.container.__chartist__ = this), this.initializeTimeoutId = setTimeout(g.bind(this), 0) } var i = a.window; b.Base = b.Class.extend({ constructor: h, optionsProvider: void 0, container: void 0, svg: void 0, eventEmitter: void 0, createChart: function () { throw new Error("Base chart type can't be instantiated!") }, update: c, detach: d, on: e, off: f, version: b.version, supportsForeignObject: !1 }) }(this || global, a), function (a, b) { "use strict"; function c(a, c, d, e, f) { a instanceof Element ? this._node = a : (this._node = y.createElementNS(b.namespaces.svg, a), "svg" === a && this.attr({ "xmlns:ct": b.namespaces.ct })), c && this.attr(c), d && this.addClass(d), e && (f && e._node.firstChild ? e._node.insertBefore(this._node, e._node.firstChild) : e._node.appendChild(this._node)) } function d(a, c) { return "string" == typeof a ? c ? this._node.getAttributeNS(c, a) : this._node.getAttribute(a) : (Object.keys(a).forEach(function (c) { if (void 0 !== a[c]) if (c.indexOf(":") !== -1) { var d = c.split(":"); this._node.setAttributeNS(b.namespaces[d[0]], c, a[c]) } else this._node.setAttribute(c, a[c]) }.bind(this)), this) } function e(a, c, d, e) { return new b.Svg(a, c, d, this, e) } function f() { return this._node.parentNode instanceof SVGElement ? new b.Svg(this._node.parentNode) : null } function g() { for (var a = this._node; "svg" !== a.nodeName;)a = a.parentNode; return new b.Svg(a) } function h(a) { var c = this._node.querySelector(a); return c ? new b.Svg(c) : null } function i(a) { var c = this._node.querySelectorAll(a); return c.length ? new b.Svg.List(c) : null } function j() { return this._node } function k(a, c, d, e) { if ("string" == typeof a) { var f = y.createElement("div"); f.innerHTML = a, a = f.firstChild } a.setAttribute("xmlns", b.namespaces.xmlns); var g = this.elem("foreignObject", c, d, e); return g._node.appendChild(a), g } function l(a) { return this._node.appendChild(y.createTextNode(a)), this } function m() { for (; this._node.firstChild;)this._node.removeChild(this._node.firstChild); return this } function n() { return this._node.parentNode.removeChild(this._node), this.parent() } function o(a) { return this._node.parentNode.replaceChild(a._node, this._node), a } function p(a, b) { return b && this._node.firstChild ? this._node.insertBefore(a._node, this._node.firstChild) : this._node.appendChild(a._node), this } function q() { return this._node.getAttribute("class") ? this._node.getAttribute("class").trim().split(/\s+/) : [] } function r(a) { return this._node.setAttribute("class", this.classes(this._node).concat(a.trim().split(/\s+/)).filter(function (a, b, c) { return c.indexOf(a) === b }).join(" ")), this } function s(a) { var b = a.trim().split(/\s+/); return this._node.setAttribute("class", this.classes(this._node).filter(function (a) { return b.indexOf(a) === -1 }).join(" ")), this } function t() { return this._node.setAttribute("class", ""), this } function u() { return this._node.getBoundingClientRect().height } function v() { return this._node.getBoundingClientRect().width } function w(a, c, d) { return void 0 === c && (c = !0), Object.keys(a).forEach(function (e) { function f(a, c) { var f, g, h, i = {}; a.easing && (h = a.easing instanceof Array ? a.easing : b.Svg.Easing[a.easing], delete a.easing), a.begin = b.ensureUnit(a.begin, "ms"), a.dur = b.ensureUnit(a.dur, "ms"), h && (a.calcMode = "spline", a.keySplines = h.join(" "), a.keyTimes = "0;1"), c && (a.fill = "freeze", i[e] = a.from, this.attr(i), g = b.quantity(a.begin || 0).value, a.begin = "indefinite"), f = this.elem("animate", b.extend({ attributeName: e }, a)), c && setTimeout(function () { try { f._node.beginElement() } catch (b) { i[e] = a.to, this.attr(i), f.remove() } }.bind(this), g), d && f._node.addEventListener("beginEvent", function () { d.emit("animationBegin", { element: this, animate: f._node, params: a }) }.bind(this)), f._node.addEventListener("endEvent", function () { d && d.emit("animationEnd", { element: this, animate: f._node, params: a }), c && (i[e] = a.to, this.attr(i), f.remove()) }.bind(this)) } a[e] instanceof Array ? a[e].forEach(function (a) { f.bind(this)(a, !1) }.bind(this)) : f.bind(this)(a[e], c) }.bind(this)), this } function x(a) { var c = this; this.svgElements = []; for (var d = 0; d < a.length; d++)this.svgElements.push(new b.Svg(a[d])); Object.keys(b.Svg.prototype).filter(function (a) { return ["constructor", "parent", "querySelector", "querySelectorAll", "replace", "append", "classes", "height", "width"].indexOf(a) === -1 }).forEach(function (a) { c[a] = function () { var d = Array.prototype.slice.call(arguments, 0); return c.svgElements.forEach(function (c) { b.Svg.prototype[a].apply(c, d) }), c } }) } var y = a.document; b.Svg = b.Class.extend({ constructor: c, attr: d, elem: e, parent: f, root: g, querySelector: h, querySelectorAll: i, getNode: j, foreignObject: k, text: l, empty: m, remove: n, replace: o, append: p, classes: q, addClass: r, removeClass: s, removeAllClasses: t, height: u, width: v, animate: w }), b.Svg.isSupported = function (a) { return y.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#" + a, "1.1") }; var z = { easeInSine: [.47, 0, .745, .715], easeOutSine: [.39, .575, .565, 1], easeInOutSine: [.445, .05, .55, .95], easeInQuad: [.55, .085, .68, .53], easeOutQuad: [.25, .46, .45, .94], easeInOutQuad: [.455, .03, .515, .955], easeInCubic: [.55, .055, .675, .19], easeOutCubic: [.215, .61, .355, 1], easeInOutCubic: [.645, .045, .355, 1], easeInQuart: [.895, .03, .685, .22], easeOutQuart: [.165, .84, .44, 1], easeInOutQuart: [.77, 0, .175, 1], easeInQuint: [.755, .05, .855, .06], easeOutQuint: [.23, 1, .32, 1], easeInOutQuint: [.86, 0, .07, 1], easeInExpo: [.95, .05, .795, .035], easeOutExpo: [.19, 1, .22, 1], easeInOutExpo: [1, 0, 0, 1], easeInCirc: [.6, .04, .98, .335], easeOutCirc: [.075, .82, .165, 1], easeInOutCirc: [.785, .135, .15, .86], easeInBack: [.6, -.28, .735, .045], easeOutBack: [.175, .885, .32, 1.275], easeInOutBack: [.68, -.55, .265, 1.55] }; b.Svg.Easing = z, b.Svg.List = b.Class.extend({ constructor: x }) }(this || global, a), function (a, b) { "use strict"; function c(a, c, d, e, f, g) { var h = b.extend({ command: f ? a.toLowerCase() : a.toUpperCase() }, c, g ? { data: g } : {}); d.splice(e, 0, h) } function d(a, b) { a.forEach(function (c, d) { t[c.command.toLowerCase()].forEach(function (e, f) { b(c, e, d, f, a) }) }) } function e(a, c) { this.pathElements = [], this.pos = 0, this.close = a, this.options = b.extend({}, u, c) } function f(a) { return void 0 !== a ? (this.pos = Math.max(0, Math.min(this.pathElements.length, a)), this) : this.pos } function g(a) { return this.pathElements.splice(this.pos, a), this } function h(a, b, d, e) { return c("M", { x: +a, y: +b }, this.pathElements, this.pos++, d, e), this } function i(a, b, d, e) { return c("L", { x: +a, y: +b }, this.pathElements, this.pos++, d, e), this } function j(a, b, d, e, f, g, h, i) { return c("C", { x1: +a, y1: +b, x2: +d, y2: +e, x: +f, y: +g }, this.pathElements, this.pos++, h, i), this } function k(a, b, d, e, f, g, h, i, j) { return c("A", { rx: +a, ry: +b, xAr: +d, lAf: +e, sf: +f, x: +g, y: +h }, this.pathElements, this.pos++, i, j), this } function l(a) { var c = a.replace(/([A-Za-z])([0-9])/g, "$1 $2").replace(/([0-9])([A-Za-z])/g, "$1 $2").split(/[\s,]+/).reduce(function (a, b) { return b.match(/[A-Za-z]/) && a.push([]), a[a.length - 1].push(b), a }, []); "Z" === c[c.length - 1][0].toUpperCase() && c.pop(); var d = c.map(function (a) { var c = a.shift(), d = t[c.toLowerCase()]; return b.extend({ command: c }, d.reduce(function (b, c, d) { return b[c] = +a[d], b }, {})) }), e = [this.pos, 0]; return Array.prototype.push.apply(e, d), Array.prototype.splice.apply(this.pathElements, e), this.pos += d.length, this } function m() { var a = Math.pow(10, this.options.accuracy); return this.pathElements.reduce(function (b, c) { var d = t[c.command.toLowerCase()].map(function (b) { return this.options.accuracy ? Math.round(c[b] * a) / a : c[b] }.bind(this)); return b + c.command + d.join(",") }.bind(this), "") + (this.close ? "Z" : "") } function n(a, b) { return d(this.pathElements, function (c, d) { c[d] *= "x" === d[0] ? a : b }), this } function o(a, b) { return d(this.pathElements, function (c, d) { c[d] += "x" === d[0] ? a : b }), this } function p(a) { return d(this.pathElements, function (b, c, d, e, f) { var g = a(b, c, d, e, f); (g || 0 === g) && (b[c] = g) }), this } function q(a) { var c = new b.Svg.Path(a || this.close); return c.pos = this.pos, c.pathElements = this.pathElements.slice().map(function (a) { return b.extend({}, a) }), c.options = b.extend({}, this.options), c } function r(a) { var c = [new b.Svg.Path]; return this.pathElements.forEach(function (d) { d.command === a.toUpperCase() && 0 !== c[c.length - 1].pathElements.length && c.push(new b.Svg.Path), c[c.length - 1].pathElements.push(d) }), c } function s(a, c, d) { for (var e = new b.Svg.Path(c, d), f = 0; f < a.length; f++)for (var g = a[f], h = 0; h < g.pathElements.length; h++)e.pathElements.push(g.pathElements[h]); return e } var t = { m: ["x", "y"], l: ["x", "y"], c: ["x1", "y1", "x2", "y2", "x", "y"], a: ["rx", "ry", "xAr", "lAf", "sf", "x", "y"] }, u = { accuracy: 3 }; b.Svg.Path = b.Class.extend({ constructor: e, position: f, remove: g, move: h, line: i, curve: j, arc: k, scale: n, translate: o, transform: p, parse: l, stringify: m, clone: q, splitByCommand: r }), b.Svg.Path.elementDescriptions = t, b.Svg.Path.join = s }(this || global, a), function (a, b) { "use strict"; function c(a, b, c, d) { this.units = a, this.counterUnits = a === e.x ? e.y : e.x, this.chartRect = b, this.axisLength = b[a.rectEnd] - b[a.rectStart], this.gridOffset = b[a.rectOffset], this.ticks = c, this.options = d } function d(a, c, d, e, f) { var g = e["axis" + this.units.pos.toUpperCase()], h = this.ticks.map(this.projectValue.bind(this)), i = this.ticks.map(g.labelInterpolationFnc); h.forEach(function (j, k) { var l, m = { x: 0, y: 0 }; l = h[k + 1] ? h[k + 1] - j : Math.max(this.axisLength - j, 30), b.isFalseyButZero(i[k]) && "" !== i[k] || ("x" === this.units.pos ? (j = this.chartRect.x1 + j, m.x = e.axisX.labelOffset.x, "start" === e.axisX.position ? m.y = this.chartRect.padding.top + e.axisX.labelOffset.y + (d ? 5 : 20) : m.y = this.chartRect.y1 + e.axisX.labelOffset.y + (d ? 5 : 20)) : (j = this.chartRect.y1 - j, m.y = e.axisY.labelOffset.y - (d ? l : 0), "start" === e.axisY.position ? m.x = d ? this.chartRect.padding.left + e.axisY.labelOffset.x : this.chartRect.x1 - 10 : m.x = this.chartRect.x2 + e.axisY.labelOffset.x + 10), g.showGrid && b.createGrid(j, k, this, this.gridOffset, this.chartRect[this.counterUnits.len](), a, [e.classNames.grid, e.classNames[this.units.dir]], f), g.showLabel && b.createLabel(j, l, k, i, this, g.offset, m, c, [e.classNames.label, e.classNames[this.units.dir], "start" === g.position ? e.classNames[g.position] : e.classNames.end], d, f)) }.bind(this)) } var e = (a.window, a.document, { x: { pos: "x", len: "width", dir: "horizontal", rectStart: "x1", rectEnd: "x2", rectOffset: "y2" }, y: { pos: "y", len: "height", dir: "vertical", rectStart: "y2", rectEnd: "y1", rectOffset: "x1" } }); b.Axis = b.Class.extend({ constructor: c, createGridAndLabels: d, projectValue: function (a, b, c) { throw new Error("Base axis can't be instantiated!") } }), b.Axis.units = e }(this || global, a), function (a, b) { "use strict"; function c(a, c, d, e) { var f = e.highLow || b.getHighLow(c, e, a.pos); this.bounds = b.getBounds(d[a.rectEnd] - d[a.rectStart], f, e.scaleMinSpace || 20, e.onlyInteger), this.range = { min: this.bounds.min, max: this.bounds.max }, b.AutoScaleAxis["super"].constructor.call(this, a, d, this.bounds.values, e) } function d(a) { return this.axisLength * (+b.getMultiValue(a, this.units.pos) - this.bounds.min) / this.bounds.range } a.window, a.document; b.AutoScaleAxis = b.Axis.extend({ constructor: c, projectValue: d }) }(this || global, a), function (a, b) { "use strict"; function c(a, c, d, e) { var f = e.highLow || b.getHighLow(c, e, a.pos); this.divisor = e.divisor || 1, this.ticks = e.ticks || b.times(this.divisor).map(function (a, b) { return f.low + (f.high - f.low) / this.divisor * b }.bind(this)), this.ticks.sort(function (a, b) { return a - b }), this.range = { min: f.low, max: f.high }, b.FixedScaleAxis["super"].constructor.call(this, a, d, this.ticks, e), this.stepLength = this.axisLength / this.divisor } function d(a) { return this.axisLength * (+b.getMultiValue(a, this.units.pos) - this.range.min) / (this.range.max - this.range.min) } a.window, a.document; b.FixedScaleAxis = b.Axis.extend({ constructor: c, projectValue: d }) }(this || global, a), function (a, b) { "use strict"; function c(a, c, d, e) { b.StepAxis["super"].constructor.call(this, a, d, e.ticks, e); var f = Math.max(1, e.ticks.length - (e.stretch ? 1 : 0)); this.stepLength = this.axisLength / f } function d(a, b) { return this.stepLength * b } a.window, a.document; b.StepAxis = b.Axis.extend({ constructor: c, projectValue: d }) }(this || global, a), function (a, b) { "use strict"; function c(a) { var c = b.normalizeData(this.data, a.reverseData, !0); this.svg = b.createSvg(this.container, a.width, a.height, a.classNames.chart); var d, f, g = this.svg.elem("g").addClass(a.classNames.gridGroup), h = this.svg.elem("g"), i = this.svg.elem("g").addClass(a.classNames.labelGroup), j = b.createChartRect(this.svg, a, e.padding); d = void 0 === a.axisX.type ? new b.StepAxis(b.Axis.units.x, c.normalized.series, j, b.extend({}, a.axisX, { ticks: c.normalized.labels, stretch: a.fullWidth })) : a.axisX.type.call(b, b.Axis.units.x, c.normalized.series, j, a.axisX), f = void 0 === a.axisY.type ? new b.AutoScaleAxis(b.Axis.units.y, c.normalized.series, j, b.extend({}, a.axisY, { high: b.isNumeric(a.high) ? a.high : a.axisY.high, low: b.isNumeric(a.low) ? a.low : a.axisY.low })) : a.axisY.type.call(b, b.Axis.units.y, c.normalized.series, j, a.axisY), d.createGridAndLabels(g, i, this.supportsForeignObject, a, this.eventEmitter), f.createGridAndLabels(g, i, this.supportsForeignObject, a, this.eventEmitter), a.showGridBackground && b.createGridBackground(g, j, a.classNames.gridBackground, this.eventEmitter), c.raw.series.forEach(function (e, g) { var i = h.elem("g"); i.attr({ "ct:series-name": e.name, "ct:meta": b.serialize(e.meta) }), i.addClass([a.classNames.series, e.className || a.classNames.series + "-" + b.alphaNumerate(g)].join(" ")); var k = [], l = []; c.normalized.series[g].forEach(function (a, h) { var i = { x: j.x1 + d.projectValue(a, h, c.normalized.series[g]), y: j.y1 - f.projectValue(a, h, c.normalized.series[g]) }; k.push(i.x, i.y), l.push({ value: a, valueIndex: h, meta: b.getMetaData(e, h) }) }.bind(this)); var m = { lineSmooth: b.getSeriesOption(e, a, "lineSmooth"), showPoint: b.getSeriesOption(e, a, "showPoint"), showLine: b.getSeriesOption(e, a, "showLine"), showArea: b.getSeriesOption(e, a, "showArea"), areaBase: b.getSeriesOption(e, a, "areaBase") }, n = "function" == typeof m.lineSmooth ? m.lineSmooth : m.lineSmooth ? b.Interpolation.monotoneCubic() : b.Interpolation.none(), o = n(k, l); if (m.showPoint && o.pathElements.forEach(function (c) { var h = i.elem("line", { x1: c.x, y1: c.y, x2: c.x + .01, y2: c.y }, a.classNames.point).attr({ "ct:value": [c.data.value.x, c.data.value.y].filter(b.isNumeric).join(","), "ct:meta": b.serialize(c.data.meta) }); this.eventEmitter.emit("draw", { type: "point", value: c.data.value, index: c.data.valueIndex, meta: c.data.meta, series: e, seriesIndex: g, axisX: d, axisY: f, group: i, element: h, x: c.x, y: c.y }) }.bind(this)), m.showLine) { var p = i.elem("path", { d: o.stringify() }, a.classNames.line, !0); this.eventEmitter.emit("draw", { type: "line", values: c.normalized.series[g], path: o.clone(), chartRect: j, index: g, series: e, seriesIndex: g, seriesMeta: e.meta, axisX: d, axisY: f, group: i, element: p }) } if (m.showArea && f.range) { var q = Math.max(Math.min(m.areaBase, f.range.max), f.range.min), r = j.y1 - f.projectValue(q); o.splitByCommand("M").filter(function (a) { return a.pathElements.length > 1 }).map(function (a) { var b = a.pathElements[0], c = a.pathElements[a.pathElements.length - 1]; return a.clone(!0).position(0).remove(1).move(b.x, r).line(b.x, b.y).position(a.pathElements.length + 1).line(c.x, r) }).forEach(function (b) { var h = i.elem("path", { d: b.stringify() }, a.classNames.area, !0); this.eventEmitter.emit("draw", { type: "area", values: c.normalized.series[g], path: b.clone(), series: e, seriesIndex: g, axisX: d, axisY: f, chartRect: j, index: g, group: i, element: h }) }.bind(this)) } }.bind(this)), this.eventEmitter.emit("created", { bounds: f.bounds, chartRect: j, axisX: d, axisY: f, svg: this.svg, options: a }) } function d(a, c, d, f) { b.Line["super"].constructor.call(this, a, c, e, b.extend({}, e, d), f) } var e = (a.window, a.document, { axisX: { offset: 30, position: "end", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: b.noop, type: void 0 }, axisY: { offset: 40, position: "start", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: b.noop, type: void 0, scaleMinSpace: 20, onlyInteger: !1 }, width: void 0, height: void 0, showLine: !0, showPoint: !0, showArea: !1, areaBase: 0, lineSmooth: !0, showGridBackground: !1, low: void 0, high: void 0, chartPadding: { top: 15, right: 15, bottom: 5, left: 10 }, fullWidth: !1, reverseData: !1, classNames: { chart: "ct-chart-line", label: "ct-label", labelGroup: "ct-labels", series: "ct-series", line: "ct-line", point: "ct-point", area: "ct-area", grid: "ct-grid", gridGroup: "ct-grids", gridBackground: "ct-grid-background", vertical: "ct-vertical", horizontal: "ct-horizontal", start: "ct-start", end: "ct-end" } }); b.Line = b.Base.extend({ constructor: d, createChart: c }) }(this || global, a), function (a, b) {
        "use strict"; function c(a) {
            var c, d; a.distributeSeries ? (c = b.normalizeData(this.data, a.reverseData, a.horizontalBars ? "x" : "y"), c.normalized.series = c.normalized.series.map(function (a) { return [a] })) : c = b.normalizeData(this.data, a.reverseData, a.horizontalBars ? "x" : "y"), this.svg = b.createSvg(this.container, a.width, a.height, a.classNames.chart + (a.horizontalBars ? " " + a.classNames.horizontalBars : "")); var f = this.svg.elem("g").addClass(a.classNames.gridGroup), g = this.svg.elem("g"), h = this.svg.elem("g").addClass(a.classNames.labelGroup);
            if (a.stackBars && 0 !== c.normalized.series.length) { var i = b.serialMap(c.normalized.series, function () { return Array.prototype.slice.call(arguments).map(function (a) { return a }).reduce(function (a, b) { return { x: a.x + (b && b.x) || 0, y: a.y + (b && b.y) || 0 } }, { x: 0, y: 0 }) }); d = b.getHighLow([i], a, a.horizontalBars ? "x" : "y") } else d = b.getHighLow(c.normalized.series, a, a.horizontalBars ? "x" : "y"); d.high = +a.high || (0 === a.high ? 0 : d.high), d.low = +a.low || (0 === a.low ? 0 : d.low); var j, k, l, m, n, o = b.createChartRect(this.svg, a, e.padding); k = a.distributeSeries && a.stackBars ? c.normalized.labels.slice(0, 1) : c.normalized.labels, a.horizontalBars ? (j = m = void 0 === a.axisX.type ? new b.AutoScaleAxis(b.Axis.units.x, c.normalized.series, o, b.extend({}, a.axisX, { highLow: d, referenceValue: 0 })) : a.axisX.type.call(b, b.Axis.units.x, c.normalized.series, o, b.extend({}, a.axisX, { highLow: d, referenceValue: 0 })), l = n = void 0 === a.axisY.type ? new b.StepAxis(b.Axis.units.y, c.normalized.series, o, { ticks: k }) : a.axisY.type.call(b, b.Axis.units.y, c.normalized.series, o, a.axisY)) : (l = m = void 0 === a.axisX.type ? new b.StepAxis(b.Axis.units.x, c.normalized.series, o, { ticks: k }) : a.axisX.type.call(b, b.Axis.units.x, c.normalized.series, o, a.axisX), j = n = void 0 === a.axisY.type ? new b.AutoScaleAxis(b.Axis.units.y, c.normalized.series, o, b.extend({}, a.axisY, { highLow: d, referenceValue: 0 })) : a.axisY.type.call(b, b.Axis.units.y, c.normalized.series, o, b.extend({}, a.axisY, { highLow: d, referenceValue: 0 }))); var p = a.horizontalBars ? o.x1 + j.projectValue(0) : o.y1 - j.projectValue(0), q = []; l.createGridAndLabels(f, h, this.supportsForeignObject, a, this.eventEmitter), j.createGridAndLabels(f, h, this.supportsForeignObject, a, this.eventEmitter), a.showGridBackground && b.createGridBackground(f, o, a.classNames.gridBackground, this.eventEmitter), c.raw.series.forEach(function (d, e) { var f, h, i = e - (c.raw.series.length - 1) / 2; f = a.distributeSeries && !a.stackBars ? l.axisLength / c.normalized.series.length / 2 : a.distributeSeries && a.stackBars ? l.axisLength / 2 : l.axisLength / c.normalized.series[e].length / 2, h = g.elem("g"), h.attr({ "ct:series-name": d.name, "ct:meta": b.serialize(d.meta) }), h.addClass([a.classNames.series, d.className || a.classNames.series + "-" + b.alphaNumerate(e)].join(" ")), c.normalized.series[e].forEach(function (g, k) { var r, s, t, u; if (u = a.distributeSeries && !a.stackBars ? e : a.distributeSeries && a.stackBars ? 0 : k, r = a.horizontalBars ? { x: o.x1 + j.projectValue(g && g.x ? g.x : 0, k, c.normalized.series[e]), y: o.y1 - l.projectValue(g && g.y ? g.y : 0, u, c.normalized.series[e]) } : { x: o.x1 + l.projectValue(g && g.x ? g.x : 0, u, c.normalized.series[e]), y: o.y1 - j.projectValue(g && g.y ? g.y : 0, k, c.normalized.series[e]) }, l instanceof b.StepAxis && (l.options.stretch || (r[l.units.pos] += f * (a.horizontalBars ? -1 : 1)), r[l.units.pos] += a.stackBars || a.distributeSeries ? 0 : i * a.seriesBarDistance * (a.horizontalBars ? -1 : 1)), t = q[k] || p, q[k] = t - (p - r[l.counterUnits.pos]), void 0 !== g) { var v = {}; v[l.units.pos + "1"] = r[l.units.pos], v[l.units.pos + "2"] = r[l.units.pos], !a.stackBars || "accumulate" !== a.stackMode && a.stackMode ? (v[l.counterUnits.pos + "1"] = p, v[l.counterUnits.pos + "2"] = r[l.counterUnits.pos]) : (v[l.counterUnits.pos + "1"] = t, v[l.counterUnits.pos + "2"] = q[k]), v.x1 = Math.min(Math.max(v.x1, o.x1), o.x2), v.x2 = Math.min(Math.max(v.x2, o.x1), o.x2), v.y1 = Math.min(Math.max(v.y1, o.y2), o.y1), v.y2 = Math.min(Math.max(v.y2, o.y2), o.y1); var w = b.getMetaData(d, k); s = h.elem("line", v, a.classNames.bar).attr({ "ct:value": [g.x, g.y].filter(b.isNumeric).join(","), "ct:meta": b.serialize(w) }), this.eventEmitter.emit("draw", b.extend({ type: "bar", value: g, index: k, meta: w, series: d, seriesIndex: e, axisX: m, axisY: n, chartRect: o, group: h, element: s }, v)) } }.bind(this)) }.bind(this)), this.eventEmitter.emit("created", { bounds: j.bounds, chartRect: o, axisX: m, axisY: n, svg: this.svg, options: a })
        } function d(a, c, d, f) { b.Bar["super"].constructor.call(this, a, c, e, b.extend({}, e, d), f) } var e = (a.window, a.document, { axisX: { offset: 30, position: "end", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: b.noop, scaleMinSpace: 30, onlyInteger: !1 }, axisY: { offset: 40, position: "start", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: b.noop, scaleMinSpace: 20, onlyInteger: !1 }, width: void 0, height: void 0, high: void 0, low: void 0, referenceValue: 0, chartPadding: { top: 15, right: 15, bottom: 5, left: 10 }, seriesBarDistance: 15, stackBars: !1, stackMode: "accumulate", horizontalBars: !1, distributeSeries: !1, reverseData: !1, showGridBackground: !1, classNames: { chart: "ct-chart-bar", horizontalBars: "ct-horizontal-bars", label: "ct-label", labelGroup: "ct-labels", series: "ct-series", bar: "ct-bar", grid: "ct-grid", gridGroup: "ct-grids", gridBackground: "ct-grid-background", vertical: "ct-vertical", horizontal: "ct-horizontal", start: "ct-start", end: "ct-end" } }); b.Bar = b.Base.extend({ constructor: d, createChart: c })
    }(this || global, a), function (a, b) { "use strict"; function c(a, b, c) { var d = b.x > a.x; return d && "explode" === c || !d && "implode" === c ? "start" : d && "implode" === c || !d && "explode" === c ? "end" : "middle" } function d(a) { var d, e, g, h, i, j = b.normalizeData(this.data), k = [], l = a.startAngle; this.svg = b.createSvg(this.container, a.width, a.height, a.donut ? a.classNames.chartDonut : a.classNames.chartPie), e = b.createChartRect(this.svg, a, f.padding), g = Math.min(e.width() / 2, e.height() / 2), i = a.total || j.normalized.series.reduce(function (a, b) { return a + b }, 0); var m = b.quantity(a.donutWidth); "%" === m.unit && (m.value *= g / 100), g -= a.donut && !a.donutSolid ? m.value / 2 : 0, h = "outside" === a.labelPosition || a.donut && !a.donutSolid ? g : "center" === a.labelPosition ? 0 : a.donutSolid ? g - m.value / 2 : g / 2, h += a.labelOffset; var n = { x: e.x1 + e.width() / 2, y: e.y2 + e.height() / 2 }, o = 1 === j.raw.series.filter(function (a) { return a.hasOwnProperty("value") ? 0 !== a.value : 0 !== a }).length; j.raw.series.forEach(function (a, b) { k[b] = this.svg.elem("g", null, null) }.bind(this)), a.showLabel && (d = this.svg.elem("g", null, null)), j.raw.series.forEach(function (e, f) { if (0 !== j.normalized.series[f] || !a.ignoreEmptyValues) { k[f].attr({ "ct:series-name": e.name }), k[f].addClass([a.classNames.series, e.className || a.classNames.series + "-" + b.alphaNumerate(f)].join(" ")); var p = i > 0 ? l + j.normalized.series[f] / i * 360 : 0, q = Math.max(0, l - (0 === f || o ? 0 : .2)); p - q >= 359.99 && (p = q + 359.99); var r, s, t, u = b.polarToCartesian(n.x, n.y, g, q), v = b.polarToCartesian(n.x, n.y, g, p), w = new b.Svg.Path(!a.donut || a.donutSolid).move(v.x, v.y).arc(g, g, 0, p - l > 180, 0, u.x, u.y); a.donut ? a.donutSolid && (t = g - m.value, r = b.polarToCartesian(n.x, n.y, t, l - (0 === f || o ? 0 : .2)), s = b.polarToCartesian(n.x, n.y, t, p), w.line(r.x, r.y), w.arc(t, t, 0, p - l > 180, 1, s.x, s.y)) : w.line(n.x, n.y); var x = a.classNames.slicePie; a.donut && (x = a.classNames.sliceDonut, a.donutSolid && (x = a.classNames.sliceDonutSolid)); var y = k[f].elem("path", { d: w.stringify() }, x); if (y.attr({ "ct:value": j.normalized.series[f], "ct:meta": b.serialize(e.meta) }), a.donut && !a.donutSolid && (y._node.style.strokeWidth = m.value + "px"), this.eventEmitter.emit("draw", { type: "slice", value: j.normalized.series[f], totalDataSum: i, index: f, meta: e.meta, series: e, group: k[f], element: y, path: w.clone(), center: n, radius: g, startAngle: l, endAngle: p }), a.showLabel) { var z; z = 1 === j.raw.series.length ? { x: n.x, y: n.y } : b.polarToCartesian(n.x, n.y, h, l + (p - l) / 2); var A; A = j.normalized.labels && !b.isFalseyButZero(j.normalized.labels[f]) ? j.normalized.labels[f] : j.normalized.series[f]; var B = a.labelInterpolationFnc(A, f); if (B || 0 === B) { var C = d.elem("text", { dx: z.x, dy: z.y, "text-anchor": c(n, z, a.labelDirection) }, a.classNames.label).text("" + B); this.eventEmitter.emit("draw", { type: "label", index: f, group: d, element: C, text: "" + B, x: z.x, y: z.y }) } } l = p } }.bind(this)), this.eventEmitter.emit("created", { chartRect: e, svg: this.svg, options: a }) } function e(a, c, d, e) { b.Pie["super"].constructor.call(this, a, c, f, b.extend({}, f, d), e) } var f = (a.window, a.document, { width: void 0, height: void 0, chartPadding: 5, classNames: { chartPie: "ct-chart-pie", chartDonut: "ct-chart-donut", series: "ct-series", slicePie: "ct-slice-pie", sliceDonut: "ct-slice-donut", sliceDonutSolid: "ct-slice-donut-solid", label: "ct-label" }, startAngle: 0, total: void 0, donut: !1, donutSolid: !1, donutWidth: 60, showLabel: !0, labelOffset: 0, labelPosition: "inside", labelInterpolationFnc: b.noop, labelDirection: "neutral", reverseData: !1, ignoreEmptyValues: !1 }); b.Pie = b.Base.extend({ constructor: e, createChart: d, determineAnchorPosition: c }) }(this || global, a), a
});
//# sourceMappingURL=chartist.min.js.map

var i, l, selectedLine = null;

/* Navigate to hash without browser history entry */
var navigateToHash = function () {
    if (window.history !== undefined && window.history.replaceState !== undefined) {
        window.history.replaceState(undefined, undefined, this.getAttribute("href"));
    }
};

var hashLinks = document.getElementsByClassName('navigatetohash');
for (i = 0, l = hashLinks.length; i < l; i++) {
    hashLinks[i].addEventListener('click', navigateToHash);
}

/* Switch test method */
var switchTestMethod = function () {
    var method = this.getAttribute("value");
    console.log("Selected test method: " + method);

    var lines, i, l, coverageData, lineAnalysis, cells;

    lines = document.querySelectorAll('.lineAnalysis tr');

    for (i = 1, l = lines.length; i < l; i++) {
        coverageData = JSON.parse(lines[i].getAttribute('data-coverage').replace(/'/g, '"'));
        lineAnalysis = coverageData[method];
        cells = lines[i].querySelectorAll('td');
        if (lineAnalysis === undefined) {
            lineAnalysis = coverageData.AllTestMethods;
            if (lineAnalysis.LVS !== 'gray') {
                cells[0].setAttribute('class', 'red');
                cells[1].innerText = cells[1].textContent = '0';
                cells[4].setAttribute('class', 'lightred');
            }
        } else {
            cells[0].setAttribute('class', lineAnalysis.LVS);
            cells[1].innerText = cells[1].textContent = lineAnalysis.VC;
            cells[4].setAttribute('class', 'light' + lineAnalysis.LVS);
        }
    }
};

var testMethods = document.getElementsByClassName('switchtestmethod');
for (i = 0, l = testMethods.length; i < l; i++) {
    testMethods[i].addEventListener('change', switchTestMethod);
}

/* Highlight test method by line */
var toggleLine = function () {
    if (selectedLine === this) {
        selectedLine = null;
    } else {
        selectedLine = null;
        unhighlightTestMethods();
        highlightTestMethods.call(this);
        selectedLine = this;
    }
    
};
var highlightTestMethods = function () {
    if (selectedLine !== null) {
        return;
    }

    var lineAnalysis;
    var coverageData = JSON.parse(this.getAttribute('data-coverage').replace(/'/g, '"'));
    var testMethods = document.getElementsByClassName('testmethod');

    for (i = 0, l = testMethods.length; i < l; i++) {
        lineAnalysis = coverageData[testMethods[i].id];
        if (lineAnalysis === undefined) {
            testMethods[i].className = testMethods[i].className.replace(/\s*light.+/g, "");
        } else {
            testMethods[i].className += ' light' + lineAnalysis.LVS;
        }
    }
};
var unhighlightTestMethods = function () {
    if (selectedLine !== null) {
        return;
    }

    var testMethods = document.getElementsByClassName('testmethod');
    for (i = 0, l = testMethods.length; i < l; i++) {
        testMethods[i].className = testMethods[i].className.replace(/\s*light.+/g, "");
    }
};
var coverableLines = document.getElementsByClassName('coverableline');
for (i = 0, l = coverableLines.length; i < l; i++) {
    coverableLines[i].addEventListener('click', toggleLine);
    coverableLines[i].addEventListener('mouseenter', highlightTestMethods);
    coverableLines[i].addEventListener('mouseleave', unhighlightTestMethods);
}

/* History charts */
var renderChart = function (chart) {
    // Remove current children (e.g. PNG placeholder)
    while (chart.firstChild) {
        chart.firstChild.remove();
    }

    var chartData = window[chart.getAttribute('data-data')];
    var options = {
        axisY: {
            type: undefined,
            onlyInteger: true
        },
        lineSmooth: false,
        low: 0,
        high: 100,
        scaleMinSpace: 20,
        onlyInteger: true,
        fullWidth: true
    };
    var lineChart = new Chartist.Line(chart, {
        labels: [],
        series: chartData.series
    }, options);

    /* Zoom */
    var zoomButtonDiv = document.createElement("div");
    zoomButtonDiv.className = "toggleZoom";
    var zoomButtonLink = document.createElement("a");
    zoomButtonLink.setAttribute("href", "");
    var zoomButtonText = document.createElement("i");
    zoomButtonText.className = "icon-search-plus";

    zoomButtonLink.appendChild(zoomButtonText);
    zoomButtonDiv.appendChild(zoomButtonLink);

    chart.appendChild(zoomButtonDiv);

    zoomButtonDiv.addEventListener('click', function (event) {
        event.preventDefault();

        if (options.axisY.type === undefined) {
            options.axisY.type = Chartist.AutoScaleAxis;
            zoomButtonText.className = "icon-search-minus";
        } else {
            options.axisY.type = undefined;
            zoomButtonText.className = "icon-search-plus";
        }

        lineChart.update(null, options);
    });

    var tooltip = document.createElement("div");
    tooltip.className = "tooltip";

    chart.appendChild(tooltip);

    /* Tooltips */
    var showToolTip = function () {
        var index = this.getAttribute('ct:meta');

        tooltip.innerHTML = chartData.tooltips[index];
        tooltip.style.display = 'block';
    };

    var moveToolTip = function (event) {
        var box = chart.getBoundingClientRect();
        var left = event.pageX - box.left - window.pageXOffset;
        var top = event.pageY - box.top - window.pageYOffset;

        left = left + 20;
        top = top - tooltip.offsetHeight / 2;

        if (left + tooltip.offsetWidth > box.width) {
            left -= tooltip.offsetWidth + 40;
        }

        if (top < 0) {
            top = 0;
        }

        if (top + tooltip.offsetHeight > box.height) {
            top = box.height - tooltip.offsetHeight;
        }

        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
    };

    var hideToolTip = function () {
        tooltip.style.display = 'none';
    };
    chart.addEventListener('mousemove', moveToolTip);

    lineChart.on('created', function () {
        var chartPoints = chart.getElementsByClassName('ct-point');
        for (i = 0, l = chartPoints.length; i < l; i++) {
            chartPoints[i].addEventListener('mousemove', showToolTip);
            chartPoints[i].addEventListener('mouseout', hideToolTip);
        }
    });
};

var charts = document.getElementsByClassName('historychart');
for (i = 0, l = charts.length; i < l; i++) {
    renderChart(charts[i]);
}

var assemblies = [
  {
    "name": "UserProfileApi",
    "classes": [
      { "name": "Program", "rp": "UserProfileApi_Program.html", "cl": 0, "ucl": 20, "cal": 20, "tl": 43, "cb": 0, "tb": 2, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Controllers.UsersController", "rp": "UserProfileApi_UsersController.html", "cl": 44, "ucl": 0, "cal": 44, "tl": 99, "cb": 2, "tb": 2, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.DTOs.UserDto", "rp": "UserProfileApi_UserDto.html", "cl": 8, "ucl": 0, "cal": 8, "tl": 14, "cb": 0, "tb": 0, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Exceptions.AppException", "rp": "UserProfileApi_AppException.html", "cl": 3, "ucl": 3, "cal": 6, "tl": 13, "cb": 0, "tb": 0, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Mappings.UserProfile", "rp": "UserProfileApi_UserProfile.html", "cl": 10, "ucl": 0, "cal": 10, "tl": 21, "cb": 0, "tb": 0, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Models.User", "rp": "UserProfileApi_User.html", "cl": 9, "ucl": 0, "cal": 9, "tl": 27, "cb": 0, "tb": 0, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Repositories.UserRepository", "rp": "UserProfileApi_UserRepository.html", "cl": 0, "ucl": 53, "cal": 53, "tl": 72, "cb": 0, "tb": 6, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Services.AuthUtil", "rp": "UserProfileApi_AuthUtil.html", "cl": 13, "ucl": 0, "cal": 13, "tl": 32, "cb": 6, "tb": 6, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Services.CloudinaryService", "rp": "UserProfileApi_CloudinaryService.html", "cl": 12, "ucl": 0, "cal": 12, "tl": 30, "cb": 8, "tb": 8, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
      { "name": "UserProfileApi.Services.UserService", "rp": "UserProfileApi_UserService.html", "cl": 61, "ucl": 0, "cal": 61, "tl": 104, "cb": 19, "tb": 20, "cm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "hc": [], "metrics": { } },
    ]},
];

var metrics = [{ "name": "Cyclomatic complexity", "abbreviation": "cc", "explanationUrl": "https://en.wikipedia.org/wiki/Cyclomatic_complexity" }, { "name": "Line coverage", "abbreviation": "cov", "explanationUrl": "https://en.wikipedia.org/wiki/Code_coverage" }, { "name": "Branch coverage", "abbreviation": "bcov", "explanationUrl": "https://en.wikipedia.org/wiki/Code_coverage" }];

var historicCoverageExecutionTimes = [];

var riskHotspotMetrics = [
];

var riskHotspots = [
];

var branchCoverageAvailable = true;
var methodCoverageAvailable = false;
var maximumDecimalPlacesForCoverageQuotas = 1;


var translations = {
'top': 'Top:',
'all': 'All',
'assembly': 'Assembly',
'class': 'Class',
'method': 'Method',
'lineCoverage': 'Line coverage',
'noGrouping': 'No grouping',
'byAssembly': 'By assembly',
'byNamespace': 'By namespace, Level:',
'all': 'All',
'collapseAll': 'Collapse all',
'expandAll': 'Expand all',
'grouping': 'Grouping:',
'filter': 'Filter:',
'name': 'Name',
'covered': 'Covered',
'uncovered': 'Uncovered',
'coverable': 'Coverable',
'total': 'Total',
'coverage': 'Line coverage',
'branchCoverage': 'Branch coverage',
'methodCoverage': 'Method coverage',
'percentage': 'Percentage',
'history': 'Coverage history',
'compareHistory': 'Compare with:',
'date': 'Date',
'allChanges': 'All changes',
'selectCoverageTypes': 'Select coverage types',
'selectCoverageTypesAndMetrics': 'Select coverage types & metrics',
'coverageTypes': 'Coverage types',
'metrics': 'Metrics',
'methodCoverageProVersion': 'Feature is only available for sponsors',
'lineCoverageIncreaseOnly': 'Line coverage: Increase only',
'lineCoverageDecreaseOnly': 'Line coverage: Decrease only',
'branchCoverageIncreaseOnly': 'Branch coverage: Increase only',
'branchCoverageDecreaseOnly': 'Branch coverage: Decrease only',
'methodCoverageIncreaseOnly': 'Method coverage: Increase only',
'methodCoverageDecreaseOnly': 'Method coverage: Decrease only'
};


(()=>{"use strict";var e,_={},p={};function n(e){var a=p[e];if(void 0!==a)return a.exports;var r=p[e]={exports:{}};return _[e](r,r.exports,n),r.exports}n.m=_,e=[],n.O=(a,r,u,l)=>{if(!r){var o=1/0;for(f=0;f<e.length;f++){for(var[r,u,l]=e[f],v=!0,t=0;t<r.length;t++)(!1&l||o>=l)&&Object.keys(n.O).every(h=>n.O[h](r[t]))?r.splice(t--,1):(v=!1,l<o&&(o=l));if(v){e.splice(f--,1);var c=u();void 0!==c&&(a=c)}}return a}l=l||0;for(var f=e.length;f>0&&e[f-1][2]>l;f--)e[f]=e[f-1];e[f]=[r,u,l]},n.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return n.d(a,{a}),a},n.d=(e,a)=>{for(var r in a)n.o(a,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:a[r]})},n.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),(()=>{var e={666:0};n.O.j=u=>0===e[u];var a=(u,l)=>{var t,c,[f,o,v]=l,s=0;if(f.some(d=>0!==e[d])){for(t in o)n.o(o,t)&&(n.m[t]=o[t]);if(v)var b=v(n)}for(u&&u(l);s<f.length;s++)n.o(e,c=f[s])&&e[c]&&e[c][0](),e[c]=0;return n.O(b)},r=self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[];r.forEach(a.bind(null,0)),r.push=a.bind(null,r.push.bind(r))})()})();
"use strict";(self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[]).push([[429],{321:(ie,Ee,de)=>{de(332)},332:()=>{!function(t){const n=t.performance;function i(M){n&&n.mark&&n.mark(M)}function o(M,T){n&&n.measure&&n.measure(M,T)}i("Zone");const c=t.__Zone_symbol_prefix||"__zone_symbol__";function a(M){return c+M}const y=!0===t[a("forceDuplicateZoneCheck")];if(t.Zone){if(y||"function"!=typeof t.Zone.__symbol__)throw new Error("Zone already loaded.");return t.Zone}let d=(()=>{class M{static assertZonePatched(){if(t.Promise!==oe.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=M.current;for(;e.parent;)e=e.parent;return e}static get current(){return U.zone}static get currentTask(){return re}static __load_patch(e,r,k=!1){if(oe.hasOwnProperty(e)){if(!k&&y)throw Error("Already loaded patch: "+e)}else if(!t["__Zone_disable_"+e]){const C="Zone:"+e;i(C),oe[e]=r(t,M,z),o(C,C)}}get parent(){return this._parent}get name(){return this._name}constructor(e,r){this._parent=e,this._name=r?r.name||"unnamed":"<root>",this._properties=r&&r.properties||{},this._zoneDelegate=new v(this,this._parent&&this._parent._zoneDelegate,r)}get(e){const r=this.getZoneWith(e);if(r)return r._properties[e]}getZoneWith(e){let r=this;for(;r;){if(r._properties.hasOwnProperty(e))return r;r=r._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,r){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const k=this._zoneDelegate.intercept(this,e,r),C=this;return function(){return C.runGuarded(k,this,arguments,r)}}run(e,r,k,C){U={parent:U,zone:this};try{return this._zoneDelegate.invoke(this,e,r,k,C)}finally{U=U.parent}}runGuarded(e,r=null,k,C){U={parent:U,zone:this};try{try{return this._zoneDelegate.invoke(this,e,r,k,C)}catch($){if(this._zoneDelegate.handleError(this,$))throw $}}finally{U=U.parent}}runTask(e,r,k){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||K).name+"; Execution: "+this.name+")");if(e.state===x&&(e.type===Q||e.type===P))return;const C=e.state!=E;C&&e._transitionTo(E,A),e.runCount++;const $=re;re=e,U={parent:U,zone:this};try{e.type==P&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,r,k)}catch(l){if(this._zoneDelegate.handleError(this,l))throw l}}finally{e.state!==x&&e.state!==h&&(e.type==Q||e.data&&e.data.isPeriodic?C&&e._transitionTo(A,E):(e.runCount=0,this._updateTaskCount(e,-1),C&&e._transitionTo(x,E,x))),U=U.parent,re=$}}scheduleTask(e){if(e.zone&&e.zone!==this){let k=this;for(;k;){if(k===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);k=k.parent}}e._transitionTo(X,x);const r=[];e._zoneDelegates=r,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(k){throw e._transitionTo(h,X,x),this._zoneDelegate.handleError(this,k),k}return e._zoneDelegates===r&&this._updateTaskCount(e,1),e.state==X&&e._transitionTo(A,X),e}scheduleMicroTask(e,r,k,C){return this.scheduleTask(new p(I,e,r,k,C,void 0))}scheduleMacroTask(e,r,k,C,$){return this.scheduleTask(new p(P,e,r,k,C,$))}scheduleEventTask(e,r,k,C,$){return this.scheduleTask(new p(Q,e,r,k,C,$))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||K).name+"; Execution: "+this.name+")");if(e.state===A||e.state===E){e._transitionTo(G,A,E);try{this._zoneDelegate.cancelTask(this,e)}catch(r){throw e._transitionTo(h,G),this._zoneDelegate.handleError(this,r),r}return this._updateTaskCount(e,-1),e._transitionTo(x,G),e.runCount=0,e}}_updateTaskCount(e,r){const k=e._zoneDelegates;-1==r&&(e._zoneDelegates=null);for(let C=0;C<k.length;C++)k[C]._updateTaskCount(e.type,r)}}return M.__symbol__=a,M})();const b={name:"",onHasTask:(M,T,e,r)=>M.hasTask(e,r),onScheduleTask:(M,T,e,r)=>M.scheduleTask(e,r),onInvokeTask:(M,T,e,r,k,C)=>M.invokeTask(e,r,k,C),onCancelTask:(M,T,e,r)=>M.cancelTask(e,r)};class v{constructor(T,e,r){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=T,this._parentDelegate=e,this._forkZS=r&&(r&&r.onFork?r:e._forkZS),this._forkDlgt=r&&(r.onFork?e:e._forkDlgt),this._forkCurrZone=r&&(r.onFork?this.zone:e._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:e._interceptZS),this._interceptDlgt=r&&(r.onIntercept?e:e._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this.zone:e._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:e._invokeZS),this._invokeDlgt=r&&(r.onInvoke?e:e._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this.zone:e._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:e._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?e:e._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this.zone:e._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:e._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?e:e._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this.zone:e._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:e._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?e:e._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this.zone:e._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:e._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?e:e._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this.zone:e._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const k=r&&r.onHasTask;(k||e&&e._hasTaskZS)&&(this._hasTaskZS=k?r:b,this._hasTaskDlgt=e,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=T,r.onScheduleTask||(this._scheduleTaskZS=b,this._scheduleTaskDlgt=e,this._scheduleTaskCurrZone=this.zone),r.onInvokeTask||(this._invokeTaskZS=b,this._invokeTaskDlgt=e,this._invokeTaskCurrZone=this.zone),r.onCancelTask||(this._cancelTaskZS=b,this._cancelTaskDlgt=e,this._cancelTaskCurrZone=this.zone))}fork(T,e){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,T,e):new d(T,e)}intercept(T,e,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,T,e,r):e}invoke(T,e,r,k,C){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,T,e,r,k,C):e.apply(r,k)}handleError(T,e){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,T,e)}scheduleTask(T,e){let r=e;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,T,e),r||(r=e);else if(e.scheduleFn)e.scheduleFn(e);else{if(e.type!=I)throw new Error("Task is missing scheduleFn.");R(e)}return r}invokeTask(T,e,r,k){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,T,e,r,k):e.callback.apply(r,k)}cancelTask(T,e){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,T,e);else{if(!e.cancelFn)throw Error("Task is not cancelable");r=e.cancelFn(e)}return r}hasTask(T,e){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,T,e)}catch(r){this.handleError(T,r)}}_updateTaskCount(T,e){const r=this._taskCounts,k=r[T],C=r[T]=k+e;if(C<0)throw new Error("More tasks executed then were scheduled.");0!=k&&0!=C||this.hasTask(this.zone,{microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:T})}}class p{constructor(T,e,r,k,C,$){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=T,this.source=e,this.data=k,this.scheduleFn=C,this.cancelFn=$,!r)throw new Error("callback is not defined");this.callback=r;const l=this;this.invoke=T===Q&&k&&k.useG?p.invokeTask:function(){return p.invokeTask.call(t,l,this,arguments)}}static invokeTask(T,e,r){T||(T=this),ee++;try{return T.runCount++,T.zone.runTask(T,e,r)}finally{1==ee&&_(),ee--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(x,X)}_transitionTo(T,e,r){if(this._state!==e&&this._state!==r)throw new Error(`${this.type} '${this.source}': can not transition to '${T}', expecting state '${e}'${r?" or '"+r+"'":""}, was '${this._state}'.`);this._state=T,T==x&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const L=a("setTimeout"),Z=a("Promise"),N=a("then");let J,B=[],H=!1;function q(M){if(J||t[Z]&&(J=t[Z].resolve(0)),J){let T=J[N];T||(T=J.then),T.call(J,M)}else t[L](M,0)}function R(M){0===ee&&0===B.length&&q(_),M&&B.push(M)}function _(){if(!H){for(H=!0;B.length;){const M=B;B=[];for(let T=0;T<M.length;T++){const e=M[T];try{e.zone.runTask(e,null,null)}catch(r){z.onUnhandledError(r)}}}z.microtaskDrainDone(),H=!1}}const K={name:"NO ZONE"},x="notScheduled",X="scheduling",A="scheduled",E="running",G="canceling",h="unknown",I="microTask",P="macroTask",Q="eventTask",oe={},z={symbol:a,currentZoneFrame:()=>U,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:R,showUncaughtError:()=>!d[a("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:q};let U={parent:null,zone:new d(null,null)},re=null,ee=0;function W(){}o("Zone","Zone"),t.Zone=d}(typeof window<"u"&&window||typeof self<"u"&&self||global);const ie=Object.getOwnPropertyDescriptor,Ee=Object.defineProperty,de=Object.getPrototypeOf,ge=Object.create,Ve=Array.prototype.slice,Se="addEventListener",Oe="removeEventListener",Ze=Zone.__symbol__(Se),Ne=Zone.__symbol__(Oe),ce="true",ae="false",ke=Zone.__symbol__("");function Ie(t,n){return Zone.current.wrap(t,n)}function Me(t,n,i,o,c){return Zone.current.scheduleMacroTask(t,n,i,o,c)}const j=Zone.__symbol__,Pe=typeof window<"u",Te=Pe?window:void 0,Y=Pe&&Te||"object"==typeof self&&self||global,ct="removeAttribute";function Le(t,n){for(let i=t.length-1;i>=0;i--)"function"==typeof t[i]&&(t[i]=Ie(t[i],n+"_"+i));return t}function Fe(t){return!t||!1!==t.writable&&!("function"==typeof t.get&&typeof t.set>"u")}const Be=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,we=!("nw"in Y)&&typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process),Ae=!we&&!Be&&!(!Pe||!Te.HTMLElement),Ue=typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process)&&!Be&&!(!Pe||!Te.HTMLElement),Re={},We=function(t){if(!(t=t||Y.event))return;let n=Re[t.type];n||(n=Re[t.type]=j("ON_PROPERTY"+t.type));const i=this||t.target||Y,o=i[n];let c;return Ae&&i===Te&&"error"===t.type?(c=o&&o.call(this,t.message,t.filename,t.lineno,t.colno,t.error),!0===c&&t.preventDefault()):(c=o&&o.apply(this,arguments),null!=c&&!c&&t.preventDefault()),c};function qe(t,n,i){let o=ie(t,n);if(!o&&i&&ie(i,n)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const c=j("on"+n+"patched");if(t.hasOwnProperty(c)&&t[c])return;delete o.writable,delete o.value;const a=o.get,y=o.set,d=n.slice(2);let b=Re[d];b||(b=Re[d]=j("ON_PROPERTY"+d)),o.set=function(v){let p=this;!p&&t===Y&&(p=Y),p&&("function"==typeof p[b]&&p.removeEventListener(d,We),y&&y.call(p,null),p[b]=v,"function"==typeof v&&p.addEventListener(d,We,!1))},o.get=function(){let v=this;if(!v&&t===Y&&(v=Y),!v)return null;const p=v[b];if(p)return p;if(a){let L=a.call(this);if(L)return o.set.call(this,L),"function"==typeof v[ct]&&v.removeAttribute(n),L}return null},Ee(t,n,o),t[c]=!0}function Xe(t,n,i){if(n)for(let o=0;o<n.length;o++)qe(t,"on"+n[o],i);else{const o=[];for(const c in t)"on"==c.slice(0,2)&&o.push(c);for(let c=0;c<o.length;c++)qe(t,o[c],i)}}const ne=j("originalInstance");function ve(t){const n=Y[t];if(!n)return;Y[j(t)]=n,Y[t]=function(){const c=Le(arguments,t);switch(c.length){case 0:this[ne]=new n;break;case 1:this[ne]=new n(c[0]);break;case 2:this[ne]=new n(c[0],c[1]);break;case 3:this[ne]=new n(c[0],c[1],c[2]);break;case 4:this[ne]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},ue(Y[t],n);const i=new n(function(){});let o;for(o in i)"XMLHttpRequest"===t&&"responseBlob"===o||function(c){"function"==typeof i[c]?Y[t].prototype[c]=function(){return this[ne][c].apply(this[ne],arguments)}:Ee(Y[t].prototype,c,{set:function(a){"function"==typeof a?(this[ne][c]=Ie(a,t+"."+c),ue(this[ne][c],a)):this[ne][c]=a},get:function(){return this[ne][c]}})}(o);for(o in n)"prototype"!==o&&n.hasOwnProperty(o)&&(Y[t][o]=n[o])}function le(t,n,i){let o=t;for(;o&&!o.hasOwnProperty(n);)o=de(o);!o&&t[n]&&(o=t);const c=j(n);let a=null;if(o&&(!(a=o[c])||!o.hasOwnProperty(c))&&(a=o[c]=o[n],Fe(o&&ie(o,n)))){const d=i(a,c,n);o[n]=function(){return d(this,arguments)},ue(o[n],a)}return a}function lt(t,n,i){let o=null;function c(a){const y=a.data;return y.args[y.cbIdx]=function(){a.invoke.apply(this,arguments)},o.apply(y.target,y.args),a}o=le(t,n,a=>function(y,d){const b=i(y,d);return b.cbIdx>=0&&"function"==typeof d[b.cbIdx]?Me(b.name,d[b.cbIdx],b,c):a.apply(y,d)})}function ue(t,n){t[j("OriginalDelegate")]=n}let ze=!1,je=!1;function ft(){if(ze)return je;ze=!0;try{const t=Te.navigator.userAgent;(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/")||-1!==t.indexOf("Edge/"))&&(je=!0)}catch{}return je}Zone.__load_patch("ZoneAwarePromise",(t,n,i)=>{const o=Object.getOwnPropertyDescriptor,c=Object.defineProperty,y=i.symbol,d=[],b=!0===t[y("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],v=y("Promise"),p=y("then"),L="__creationTrace__";i.onUnhandledError=l=>{if(i.showUncaughtError()){const u=l&&l.rejection;u?console.error("Unhandled Promise rejection:",u instanceof Error?u.message:u,"; Zone:",l.zone.name,"; Task:",l.task&&l.task.source,"; Value:",u,u instanceof Error?u.stack:void 0):console.error(l)}},i.microtaskDrainDone=()=>{for(;d.length;){const l=d.shift();try{l.zone.runGuarded(()=>{throw l.throwOriginal?l.rejection:l})}catch(u){N(u)}}};const Z=y("unhandledPromiseRejectionHandler");function N(l){i.onUnhandledError(l);try{const u=n[Z];"function"==typeof u&&u.call(this,l)}catch{}}function B(l){return l&&l.then}function H(l){return l}function J(l){return e.reject(l)}const q=y("state"),R=y("value"),_=y("finally"),K=y("parentPromiseValue"),x=y("parentPromiseState"),X="Promise.then",A=null,E=!0,G=!1,h=0;function I(l,u){return s=>{try{z(l,u,s)}catch(f){z(l,!1,f)}}}const P=function(){let l=!1;return function(s){return function(){l||(l=!0,s.apply(null,arguments))}}},Q="Promise resolved with itself",oe=y("currentTaskTrace");function z(l,u,s){const f=P();if(l===s)throw new TypeError(Q);if(l[q]===A){let g=null;try{("object"==typeof s||"function"==typeof s)&&(g=s&&s.then)}catch(w){return f(()=>{z(l,!1,w)})(),l}if(u!==G&&s instanceof e&&s.hasOwnProperty(q)&&s.hasOwnProperty(R)&&s[q]!==A)re(s),z(l,s[q],s[R]);else if(u!==G&&"function"==typeof g)try{g.call(s,f(I(l,u)),f(I(l,!1)))}catch(w){f(()=>{z(l,!1,w)})()}else{l[q]=u;const w=l[R];if(l[R]=s,l[_]===_&&u===E&&(l[q]=l[x],l[R]=l[K]),u===G&&s instanceof Error){const m=n.currentTask&&n.currentTask.data&&n.currentTask.data[L];m&&c(s,oe,{configurable:!0,enumerable:!1,writable:!0,value:m})}for(let m=0;m<w.length;)ee(l,w[m++],w[m++],w[m++],w[m++]);if(0==w.length&&u==G){l[q]=h;let m=s;try{throw new Error("Uncaught (in promise): "+function a(l){return l&&l.toString===Object.prototype.toString?(l.constructor&&l.constructor.name||"")+": "+JSON.stringify(l):l?l.toString():Object.prototype.toString.call(l)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(D){m=D}b&&(m.throwOriginal=!0),m.rejection=s,m.promise=l,m.zone=n.current,m.task=n.currentTask,d.push(m),i.scheduleMicroTask()}}}return l}const U=y("rejectionHandledHandler");function re(l){if(l[q]===h){try{const u=n[U];u&&"function"==typeof u&&u.call(this,{rejection:l[R],promise:l})}catch{}l[q]=G;for(let u=0;u<d.length;u++)l===d[u].promise&&d.splice(u,1)}}function ee(l,u,s,f,g){re(l);const w=l[q],m=w?"function"==typeof f?f:H:"function"==typeof g?g:J;u.scheduleMicroTask(X,()=>{try{const D=l[R],S=!!s&&_===s[_];S&&(s[K]=D,s[x]=w);const O=u.run(m,void 0,S&&m!==J&&m!==H?[]:[D]);z(s,!0,O)}catch(D){z(s,!1,D)}},s)}const M=function(){},T=t.AggregateError;class e{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(u){return z(new this(null),E,u)}static reject(u){return z(new this(null),G,u)}static any(u){if(!u||"function"!=typeof u[Symbol.iterator])return Promise.reject(new T([],"All promises were rejected"));const s=[];let f=0;try{for(let m of u)f++,s.push(e.resolve(m))}catch{return Promise.reject(new T([],"All promises were rejected"))}if(0===f)return Promise.reject(new T([],"All promises were rejected"));let g=!1;const w=[];return new e((m,D)=>{for(let S=0;S<s.length;S++)s[S].then(O=>{g||(g=!0,m(O))},O=>{w.push(O),f--,0===f&&(g=!0,D(new T(w,"All promises were rejected")))})})}static race(u){let s,f,g=new this((D,S)=>{s=D,f=S});function w(D){s(D)}function m(D){f(D)}for(let D of u)B(D)||(D=this.resolve(D)),D.then(w,m);return g}static all(u){return e.allWithCallback(u)}static allSettled(u){return(this&&this.prototype instanceof e?this:e).allWithCallback(u,{thenCallback:f=>({status:"fulfilled",value:f}),errorCallback:f=>({status:"rejected",reason:f})})}static allWithCallback(u,s){let f,g,w=new this((O,V)=>{f=O,g=V}),m=2,D=0;const S=[];for(let O of u){B(O)||(O=this.resolve(O));const V=D;try{O.then(F=>{S[V]=s?s.thenCallback(F):F,m--,0===m&&f(S)},F=>{s?(S[V]=s.errorCallback(F),m--,0===m&&f(S)):g(F)})}catch(F){g(F)}m++,D++}return m-=2,0===m&&f(S),w}constructor(u){const s=this;if(!(s instanceof e))throw new Error("Must be an instanceof Promise.");s[q]=A,s[R]=[];try{const f=P();u&&u(f(I(s,E)),f(I(s,G)))}catch(f){z(s,!1,f)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return e}then(u,s){let f=this.constructor?.[Symbol.species];(!f||"function"!=typeof f)&&(f=this.constructor||e);const g=new f(M),w=n.current;return this[q]==A?this[R].push(w,g,u,s):ee(this,w,g,u,s),g}catch(u){return this.then(null,u)}finally(u){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=e);const f=new s(M);f[_]=_;const g=n.current;return this[q]==A?this[R].push(g,f,u,u):ee(this,g,f,u,u),f}}e.resolve=e.resolve,e.reject=e.reject,e.race=e.race,e.all=e.all;const r=t[v]=t.Promise;t.Promise=e;const k=y("thenPatched");function C(l){const u=l.prototype,s=o(u,"then");if(s&&(!1===s.writable||!s.configurable))return;const f=u.then;u[p]=f,l.prototype.then=function(g,w){return new e((D,S)=>{f.call(this,D,S)}).then(g,w)},l[k]=!0}return i.patchThen=C,r&&(C(r),le(t,"fetch",l=>function $(l){return function(u,s){let f=l.apply(u,s);if(f instanceof e)return f;let g=f.constructor;return g[k]||C(g),f}}(l))),Promise[n.__symbol__("uncaughtPromiseErrors")]=d,e}),Zone.__load_patch("toString",t=>{const n=Function.prototype.toString,i=j("OriginalDelegate"),o=j("Promise"),c=j("Error"),a=function(){if("function"==typeof this){const v=this[i];if(v)return"function"==typeof v?n.call(v):Object.prototype.toString.call(v);if(this===Promise){const p=t[o];if(p)return n.call(p)}if(this===Error){const p=t[c];if(p)return n.call(p)}}return n.call(this)};a[i]=n,Function.prototype.toString=a;const y=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":y.call(this)}});let ye=!1;if(typeof window<"u")try{const t=Object.defineProperty({},"passive",{get:function(){ye=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch{ye=!1}const ht={useG:!0},te={},Ye={},$e=new RegExp("^"+ke+"(\\w+)(true|false)$"),Ke=j("propagationStopped");function Je(t,n){const i=(n?n(t):t)+ae,o=(n?n(t):t)+ce,c=ke+i,a=ke+o;te[t]={},te[t][ae]=c,te[t][ce]=a}function dt(t,n,i,o){const c=o&&o.add||Se,a=o&&o.rm||Oe,y=o&&o.listeners||"eventListeners",d=o&&o.rmAll||"removeAllListeners",b=j(c),v="."+c+":",p="prependListener",L="."+p+":",Z=function(R,_,K){if(R.isRemoved)return;const x=R.callback;let X;"object"==typeof x&&x.handleEvent&&(R.callback=E=>x.handleEvent(E),R.originalDelegate=x);try{R.invoke(R,_,[K])}catch(E){X=E}const A=R.options;return A&&"object"==typeof A&&A.once&&_[a].call(_,K.type,R.originalDelegate?R.originalDelegate:R.callback,A),X};function N(R,_,K){if(!(_=_||t.event))return;const x=R||_.target||t,X=x[te[_.type][K?ce:ae]];if(X){const A=[];if(1===X.length){const E=Z(X[0],x,_);E&&A.push(E)}else{const E=X.slice();for(let G=0;G<E.length&&(!_||!0!==_[Ke]);G++){const h=Z(E[G],x,_);h&&A.push(h)}}if(1===A.length)throw A[0];for(let E=0;E<A.length;E++){const G=A[E];n.nativeScheduleMicroTask(()=>{throw G})}}}const B=function(R){return N(this,R,!1)},H=function(R){return N(this,R,!0)};function J(R,_){if(!R)return!1;let K=!0;_&&void 0!==_.useG&&(K=_.useG);const x=_&&_.vh;let X=!0;_&&void 0!==_.chkDup&&(X=_.chkDup);let A=!1;_&&void 0!==_.rt&&(A=_.rt);let E=R;for(;E&&!E.hasOwnProperty(c);)E=de(E);if(!E&&R[c]&&(E=R),!E||E[b])return!1;const G=_&&_.eventNameToString,h={},I=E[b]=E[c],P=E[j(a)]=E[a],Q=E[j(y)]=E[y],oe=E[j(d)]=E[d];let z;_&&_.prepend&&(z=E[j(_.prepend)]=E[_.prepend]);const e=K?function(s){if(!h.isExisting)return I.call(h.target,h.eventName,h.capture?H:B,h.options)}:function(s){return I.call(h.target,h.eventName,s.invoke,h.options)},r=K?function(s){if(!s.isRemoved){const f=te[s.eventName];let g;f&&(g=f[s.capture?ce:ae]);const w=g&&s.target[g];if(w)for(let m=0;m<w.length;m++)if(w[m]===s){w.splice(m,1),s.isRemoved=!0,0===w.length&&(s.allRemoved=!0,s.target[g]=null);break}}if(s.allRemoved)return P.call(s.target,s.eventName,s.capture?H:B,s.options)}:function(s){return P.call(s.target,s.eventName,s.invoke,s.options)},C=_&&_.diff?_.diff:function(s,f){const g=typeof f;return"function"===g&&s.callback===f||"object"===g&&s.originalDelegate===f},$=Zone[j("UNPATCHED_EVENTS")],l=t[j("PASSIVE_EVENTS")],u=function(s,f,g,w,m=!1,D=!1){return function(){const S=this||t;let O=arguments[0];_&&_.transferEventName&&(O=_.transferEventName(O));let V=arguments[1];if(!V)return s.apply(this,arguments);if(we&&"uncaughtException"===O)return s.apply(this,arguments);let F=!1;if("function"!=typeof V){if(!V.handleEvent)return s.apply(this,arguments);F=!0}if(x&&!x(s,V,S,arguments))return;const fe=ye&&!!l&&-1!==l.indexOf(O),se=function U(s,f){return!ye&&"object"==typeof s&&s?!!s.capture:ye&&f?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],fe);if($)for(let _e=0;_e<$.length;_e++)if(O===$[_e])return fe?s.call(S,O,V,se):s.apply(this,arguments);const xe=!!se&&("boolean"==typeof se||se.capture),nt=!(!se||"object"!=typeof se)&&se.once,gt=Zone.current;let Ge=te[O];Ge||(Je(O,G),Ge=te[O]);const rt=Ge[xe?ce:ae];let De,me=S[rt],ot=!1;if(me){if(ot=!0,X)for(let _e=0;_e<me.length;_e++)if(C(me[_e],V))return}else me=S[rt]=[];const st=S.constructor.name,it=Ye[st];it&&(De=it[O]),De||(De=st+f+(G?G(O):O)),h.options=se,nt&&(h.options.once=!1),h.target=S,h.capture=xe,h.eventName=O,h.isExisting=ot;const be=K?ht:void 0;be&&(be.taskData=h);const he=gt.scheduleEventTask(De,V,be,g,w);return h.target=null,be&&(be.taskData=null),nt&&(se.once=!0),!ye&&"boolean"==typeof he.options||(he.options=se),he.target=S,he.capture=xe,he.eventName=O,F&&(he.originalDelegate=V),D?me.unshift(he):me.push(he),m?S:void 0}};return E[c]=u(I,v,e,r,A),z&&(E[p]=u(z,L,function(s){return z.call(h.target,h.eventName,s.invoke,h.options)},r,A,!0)),E[a]=function(){const s=this||t;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const g=arguments[2],w=!!g&&("boolean"==typeof g||g.capture),m=arguments[1];if(!m)return P.apply(this,arguments);if(x&&!x(P,m,s,arguments))return;const D=te[f];let S;D&&(S=D[w?ce:ae]);const O=S&&s[S];if(O)for(let V=0;V<O.length;V++){const F=O[V];if(C(F,m))return O.splice(V,1),F.isRemoved=!0,0===O.length&&(F.allRemoved=!0,s[S]=null,"string"==typeof f)&&(s[ke+"ON_PROPERTY"+f]=null),F.zone.cancelTask(F),A?s:void 0}return P.apply(this,arguments)},E[y]=function(){const s=this||t;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const g=[],w=Qe(s,G?G(f):f);for(let m=0;m<w.length;m++){const D=w[m];g.push(D.originalDelegate?D.originalDelegate:D.callback)}return g},E[d]=function(){const s=this||t;let f=arguments[0];if(f){_&&_.transferEventName&&(f=_.transferEventName(f));const g=te[f];if(g){const D=s[g[ae]],S=s[g[ce]];if(D){const O=D.slice();for(let V=0;V<O.length;V++){const F=O[V];this[a].call(this,f,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}if(S){const O=S.slice();for(let V=0;V<O.length;V++){const F=O[V];this[a].call(this,f,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}}}else{const g=Object.keys(s);for(let w=0;w<g.length;w++){const D=$e.exec(g[w]);let S=D&&D[1];S&&"removeListener"!==S&&this[d].call(this,S)}this[d].call(this,"removeListener")}if(A)return this},ue(E[c],I),ue(E[a],P),oe&&ue(E[d],oe),Q&&ue(E[y],Q),!0}let q=[];for(let R=0;R<i.length;R++)q[R]=J(i[R],o);return q}function Qe(t,n){if(!n){const a=[];for(let y in t){const d=$e.exec(y);let b=d&&d[1];if(b&&(!n||b===n)){const v=t[y];if(v)for(let p=0;p<v.length;p++)a.push(v[p])}}return a}let i=te[n];i||(Je(n),i=te[n]);const o=t[i[ae]],c=t[i[ce]];return o?c?o.concat(c):o.slice():c?c.slice():[]}function _t(t,n){const i=t.Event;i&&i.prototype&&n.patchMethod(i.prototype,"stopImmediatePropagation",o=>function(c,a){c[Ke]=!0,o&&o.apply(c,a)})}function Et(t,n,i,o,c){const a=Zone.__symbol__(o);if(n[a])return;const y=n[a]=n[o];n[o]=function(d,b,v){return b&&b.prototype&&c.forEach(function(p){const L=`${i}.${o}::`+p,Z=b.prototype;try{if(Z.hasOwnProperty(p)){const N=t.ObjectGetOwnPropertyDescriptor(Z,p);N&&N.value?(N.value=t.wrapWithCurrentZone(N.value,L),t._redefineProperty(b.prototype,p,N)):Z[p]&&(Z[p]=t.wrapWithCurrentZone(Z[p],L))}else Z[p]&&(Z[p]=t.wrapWithCurrentZone(Z[p],L))}catch{}}),y.call(n,d,b,v)},t.attachOriginToPatched(n[o],y)}function et(t,n,i){if(!i||0===i.length)return n;const o=i.filter(a=>a.target===t);if(!o||0===o.length)return n;const c=o[0].ignoreProperties;return n.filter(a=>-1===c.indexOf(a))}function tt(t,n,i,o){t&&Xe(t,et(t,n,i),o)}function He(t){return Object.getOwnPropertyNames(t).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}Zone.__load_patch("util",(t,n,i)=>{const o=He(t);i.patchOnProperties=Xe,i.patchMethod=le,i.bindArguments=Le,i.patchMacroTask=lt;const c=n.__symbol__("BLACK_LISTED_EVENTS"),a=n.__symbol__("UNPATCHED_EVENTS");t[a]&&(t[c]=t[a]),t[c]&&(n[c]=n[a]=t[c]),i.patchEventPrototype=_t,i.patchEventTarget=dt,i.isIEOrEdge=ft,i.ObjectDefineProperty=Ee,i.ObjectGetOwnPropertyDescriptor=ie,i.ObjectCreate=ge,i.ArraySlice=Ve,i.patchClass=ve,i.wrapWithCurrentZone=Ie,i.filterProperties=et,i.attachOriginToPatched=ue,i._redefineProperty=Object.defineProperty,i.patchCallbacks=Et,i.getGlobalObjects=()=>({globalSources:Ye,zoneSymbolEventNames:te,eventNames:o,isBrowser:Ae,isMix:Ue,isNode:we,TRUE_STR:ce,FALSE_STR:ae,ZONE_SYMBOL_PREFIX:ke,ADD_EVENT_LISTENER_STR:Se,REMOVE_EVENT_LISTENER_STR:Oe})});const Ce=j("zoneTask");function pe(t,n,i,o){let c=null,a=null;i+=o;const y={};function d(v){const p=v.data;return p.args[0]=function(){return v.invoke.apply(this,arguments)},p.handleId=c.apply(t,p.args),v}function b(v){return a.call(t,v.data.handleId)}c=le(t,n+=o,v=>function(p,L){if("function"==typeof L[0]){const Z={isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?L[1]||0:void 0,args:L},N=L[0];L[0]=function(){try{return N.apply(this,arguments)}finally{Z.isPeriodic||("number"==typeof Z.handleId?delete y[Z.handleId]:Z.handleId&&(Z.handleId[Ce]=null))}};const B=Me(n,L[0],Z,d,b);if(!B)return B;const H=B.data.handleId;return"number"==typeof H?y[H]=B:H&&(H[Ce]=B),H&&H.ref&&H.unref&&"function"==typeof H.ref&&"function"==typeof H.unref&&(B.ref=H.ref.bind(H),B.unref=H.unref.bind(H)),"number"==typeof H||H?H:B}return v.apply(t,L)}),a=le(t,i,v=>function(p,L){const Z=L[0];let N;"number"==typeof Z?N=y[Z]:(N=Z&&Z[Ce],N||(N=Z)),N&&"string"==typeof N.type?"notScheduled"!==N.state&&(N.cancelFn&&N.data.isPeriodic||0===N.runCount)&&("number"==typeof Z?delete y[Z]:Z&&(Z[Ce]=null),N.zone.cancelTask(N)):v.apply(t,L)})}Zone.__load_patch("legacy",t=>{const n=t[Zone.__symbol__("legacyPatch")];n&&n()}),Zone.__load_patch("queueMicrotask",(t,n,i)=>{i.patchMethod(t,"queueMicrotask",o=>function(c,a){n.current.scheduleMicroTask("queueMicrotask",a[0])})}),Zone.__load_patch("timers",t=>{const n="set",i="clear";pe(t,n,i,"Timeout"),pe(t,n,i,"Interval"),pe(t,n,i,"Immediate")}),Zone.__load_patch("requestAnimationFrame",t=>{pe(t,"request","cancel","AnimationFrame"),pe(t,"mozRequest","mozCancel","AnimationFrame"),pe(t,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",(t,n)=>{const i=["alert","prompt","confirm"];for(let o=0;o<i.length;o++)le(t,i[o],(a,y,d)=>function(b,v){return n.current.run(a,t,v,d)})}),Zone.__load_patch("EventTarget",(t,n,i)=>{(function mt(t,n){n.patchEventPrototype(t,n)})(t,i),function pt(t,n){if(Zone[n.symbol("patchEventTarget")])return;const{eventNames:i,zoneSymbolEventNames:o,TRUE_STR:c,FALSE_STR:a,ZONE_SYMBOL_PREFIX:y}=n.getGlobalObjects();for(let b=0;b<i.length;b++){const v=i[b],Z=y+(v+a),N=y+(v+c);o[v]={},o[v][a]=Z,o[v][c]=N}const d=t.EventTarget;d&&d.prototype&&n.patchEventTarget(t,n,[d&&d.prototype])}(t,i);const o=t.XMLHttpRequestEventTarget;o&&o.prototype&&i.patchEventTarget(t,i,[o.prototype])}),Zone.__load_patch("MutationObserver",(t,n,i)=>{ve("MutationObserver"),ve("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",(t,n,i)=>{ve("IntersectionObserver")}),Zone.__load_patch("FileReader",(t,n,i)=>{ve("FileReader")}),Zone.__load_patch("on_property",(t,n,i)=>{!function Tt(t,n){if(we&&!Ue||Zone[t.symbol("patchEvents")])return;const i=n.__Zone_ignore_on_properties;let o=[];if(Ae){const c=window;o=o.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const a=function ut(){try{const t=Te.navigator.userAgent;if(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:c,ignoreProperties:["error"]}]:[];tt(c,He(c),i&&i.concat(a),de(c))}o=o.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<o.length;c++){const a=n[o[c]];a&&a.prototype&&tt(a.prototype,He(a.prototype),i)}}(i,t)}),Zone.__load_patch("customElements",(t,n,i)=>{!function yt(t,n){const{isBrowser:i,isMix:o}=n.getGlobalObjects();(i||o)&&t.customElements&&"customElements"in t&&n.patchCallbacks(n,t.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(t,i)}),Zone.__load_patch("XHR",(t,n)=>{!function b(v){const p=v.XMLHttpRequest;if(!p)return;const L=p.prototype;let N=L[Ze],B=L[Ne];if(!N){const h=v.XMLHttpRequestEventTarget;if(h){const I=h.prototype;N=I[Ze],B=I[Ne]}}const H="readystatechange",J="scheduled";function q(h){const I=h.data,P=I.target;P[a]=!1,P[d]=!1;const Q=P[c];N||(N=P[Ze],B=P[Ne]),Q&&B.call(P,H,Q);const oe=P[c]=()=>{if(P.readyState===P.DONE)if(!I.aborted&&P[a]&&h.state===J){const U=P[n.__symbol__("loadfalse")];if(0!==P.status&&U&&U.length>0){const re=h.invoke;h.invoke=function(){const ee=P[n.__symbol__("loadfalse")];for(let W=0;W<ee.length;W++)ee[W]===h&&ee.splice(W,1);!I.aborted&&h.state===J&&re.call(h)},U.push(h)}else h.invoke()}else!I.aborted&&!1===P[a]&&(P[d]=!0)};return N.call(P,H,oe),P[i]||(P[i]=h),E.apply(P,I.args),P[a]=!0,h}function R(){}function _(h){const I=h.data;return I.aborted=!0,G.apply(I.target,I.args)}const K=le(L,"open",()=>function(h,I){return h[o]=0==I[2],h[y]=I[1],K.apply(h,I)}),X=j("fetchTaskAborting"),A=j("fetchTaskScheduling"),E=le(L,"send",()=>function(h,I){if(!0===n.current[A]||h[o])return E.apply(h,I);{const P={target:h,url:h[y],isPeriodic:!1,args:I,aborted:!1},Q=Me("XMLHttpRequest.send",R,P,q,_);h&&!0===h[d]&&!P.aborted&&Q.state===J&&Q.invoke()}}),G=le(L,"abort",()=>function(h,I){const P=function Z(h){return h[i]}(h);if(P&&"string"==typeof P.type){if(null==P.cancelFn||P.data&&P.data.aborted)return;P.zone.cancelTask(P)}else if(!0===n.current[X])return G.apply(h,I)})}(t);const i=j("xhrTask"),o=j("xhrSync"),c=j("xhrListener"),a=j("xhrScheduled"),y=j("xhrURL"),d=j("xhrErrorBeforeScheduled")}),Zone.__load_patch("geolocation",t=>{t.navigator&&t.navigator.geolocation&&function at(t,n){const i=t.constructor.name;for(let o=0;o<n.length;o++){const c=n[o],a=t[c];if(a){if(!Fe(ie(t,c)))continue;t[c]=(d=>{const b=function(){return d.apply(this,Le(arguments,i+"."+c))};return ue(b,d),b})(a)}}}(t.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",(t,n)=>{function i(o){return function(c){Qe(t,o).forEach(y=>{const d=t.PromiseRejectionEvent;if(d){const b=new d(o,{promise:c.promise,reason:c.rejection});y.invoke(b)}})}}t.PromiseRejectionEvent&&(n[j("unhandledPromiseRejectionHandler")]=i("unhandledrejection"),n[j("rejectionHandledHandler")]=i("rejectionhandled"))})}},ie=>{ie(ie.s=321)}]);

"use strict";(self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[]).push([[179],{538:()=>{function _e(e){return"function"==typeof e}function si(e){const n=e(r=>{Error.call(r),r.stack=(new Error).stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}const ai=si(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:\n${n.map((r,o)=>`${o+1}) ${r.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=n});function Zr(e,t){if(e){const n=e.indexOf(t);0<=n&&e.splice(n,1)}}class Nt{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._teardowns=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;const{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(const i of n)i.remove(this);else n.remove(this);const{initialTeardown:r}=this;if(_e(r))try{r()}catch(i){t=i instanceof ai?i.errors:[i]}const{_teardowns:o}=this;if(o){this._teardowns=null;for(const i of o)try{pd(i)}catch(s){t=t??[],s instanceof ai?t=[...t,...s.errors]:t.push(s)}}if(t)throw new ai(t)}}add(t){var n;if(t&&t!==this)if(this.closed)pd(t);else{if(t instanceof Nt){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._teardowns=null!==(n=this._teardowns)&&void 0!==n?n:[]).push(t)}}_hasParent(t){const{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){const{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){const{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Zr(n,t)}remove(t){const{_teardowns:n}=this;n&&Zr(n,t),t instanceof Nt&&t._removeParent(this)}}Nt.EMPTY=(()=>{const e=new Nt;return e.closed=!0,e})();const fd=Nt.EMPTY;function hd(e){return e instanceof Nt||e&&"closed"in e&&_e(e.remove)&&_e(e.add)&&_e(e.unsubscribe)}function pd(e){_e(e)?e():e.unsubscribe()}const xn={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},li={setTimeout(...e){const{delegate:t}=li;return(t?.setTimeout||setTimeout)(...e)},clearTimeout(e){const{delegate:t}=li;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function gd(e){li.setTimeout(()=>{const{onUnhandledError:t}=xn;if(!t)throw e;t(e)})}function ci(){}const pC=pa("C",void 0,void 0);function pa(e,t,n){return{kind:e,value:t,error:n}}let On=null;function ui(e){if(xn.useDeprecatedSynchronousErrorHandling){const t=!On;if(t&&(On={errorThrown:!1,error:null}),e(),t){const{errorThrown:n,error:r}=On;if(On=null,n)throw r}}else e()}class ga extends Nt{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,hd(t)&&t.add(this)):this.destination=vC}static create(t,n,r){return new ma(t,n,r)}next(t){this.isStopped?va(function mC(e){return pa("N",e,void 0)}(t),this):this._next(t)}error(t){this.isStopped?va(function gC(e){return pa("E",void 0,e)}(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?va(pC,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}class ma extends ga{constructor(t,n,r){let o;if(super(),_e(t))o=t;else if(t){let i;({next:o,error:n,complete:r}=t),this&&xn.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe()):i=t,o=o?.bind(i),n=n?.bind(i),r=r?.bind(i)}this.destination={next:o?_a(o):ci,error:_a(n??md),complete:r?_a(r):ci}}}function _a(e,t){return(...n)=>{try{e(...n)}catch(r){xn.useDeprecatedSynchronousErrorHandling?function _C(e){xn.useDeprecatedSynchronousErrorHandling&&On&&(On.errorThrown=!0,On.error=e)}(r):gd(r)}}}function md(e){throw e}function va(e,t){const{onStoppedNotification:n}=xn;n&&li.setTimeout(()=>n(e,t))}const vC={closed:!0,next:ci,error:md,complete:ci},ya="function"==typeof Symbol&&Symbol.observable||"@@observable";function Ca(e){return e}let je=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){const r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){const i=function CC(e){return e&&e instanceof ga||function yC(e){return e&&_e(e.next)&&_e(e.error)&&_e(e.complete)}(e)&&hd(e)}(n)?n:new ma(n,r,o);return ui(()=>{const{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return new(r=vd(r))((o,i)=>{let s;s=this.subscribe(a=>{try{n(a)}catch(l){i(l),s?.unsubscribe()}},i,o)})}_subscribe(n){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(n)}[ya](){return this}pipe(...n){return function _d(e){return 0===e.length?Ca:1===e.length?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}(n)(this)}toPromise(n){return new(n=vd(n))((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function vd(e){var t;return null!==(t=e??xn.Promise)&&void 0!==t?t:Promise}const DC=si(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let di=(()=>{class e extends je{constructor(){super(),this.closed=!1,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){const r=new yd(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new DC}next(n){ui(()=>{if(this._throwIfClosed(),!this.isStopped){const r=this.observers.slice();for(const o of r)o.next(n)}})}error(n){ui(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;const{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){ui(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=null}get observed(){var n;return(null===(n=this.observers)||void 0===n?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){const{hasError:r,isStopped:o,observers:i}=this;return r||o?fd:(i.push(n),new Nt(()=>Zr(i,n)))}_checkFinalizedStatuses(n){const{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){const n=new je;return n.source=this,n}}return e.create=(t,n)=>new yd(t,n),e})();class yd extends di{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.next)||void 0===r||r.call(n,t)}error(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.error)||void 0===r||r.call(n,t)}complete(){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.complete)||void 0===n||n.call(t)}_subscribe(t){var n,r;return null!==(r=null===(n=this.source)||void 0===n?void 0:n.subscribe(t))&&void 0!==r?r:fd}}function un(e){return t=>{if(function wC(e){return _e(e?.lift)}(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}class zt extends ga{constructor(t,n,r,o,i){super(t),this.onFinalize=i,this._next=n?function(s){try{n(s)}catch(a){t.error(a)}}:super._next,this._error=o?function(s){try{o(s)}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(s){t.error(s)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;const{closed:n}=this;super.unsubscribe(),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}}function Da(e,t){return un((n,r)=>{let o=0;n.subscribe(new zt(r,i=>{r.next(e.call(t,i,o++))}))})}function Rn(e){return this instanceof Rn?(this.v=e,this):new Rn(e)}function MC(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,t=e[Symbol.asyncIterator];return t?t.call(e):(e=function wd(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,l){!function o(i,s,a,l){Promise.resolve(l).then(function(c){i({value:c,done:a})},s)}(a,l,(s=e[i](s)).done,s.value)})}}}const bd=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function Ed(e){return _e(e?.then)}function Id(e){return _e(e[ya])}function Md(e){return Symbol.asyncIterator&&_e(e?.[Symbol.asyncIterator])}function Sd(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const Ad=function AC(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function Td(e){return _e(e?.[Ad])}function Nd(e){return function IC(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,r=n.apply(e,t||[]),i=[];return o={},s("next"),s("throw"),s("return"),o[Symbol.asyncIterator]=function(){return this},o;function s(h){r[h]&&(o[h]=function(p){return new Promise(function(m,w){i.push([h,p,m,w])>1||a(h,p)})})}function a(h,p){try{!function l(h){h.value instanceof Rn?Promise.resolve(h.value.v).then(c,u):d(i[0][2],h)}(r[h](p))}catch(m){d(i[0][3],m)}}function c(h){a("next",h)}function u(h){a("throw",h)}function d(h,p){h(p),i.shift(),i.length&&a(i[0][0],i[0][1])}}(this,arguments,function*(){const n=e.getReader();try{for(;;){const{value:r,done:o}=yield Rn(n.read());if(o)return yield Rn(void 0);yield yield Rn(r)}}finally{n.releaseLock()}})}function xd(e){return _e(e?.getReader)}function dn(e){if(e instanceof je)return e;if(null!=e){if(Id(e))return function TC(e){return new je(t=>{const n=e[ya]();if(_e(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(bd(e))return function NC(e){return new je(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}(e);if(Ed(e))return function xC(e){return new je(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,gd)})}(e);if(Md(e))return Od(e);if(Td(e))return function OC(e){return new je(t=>{for(const n of e)if(t.next(n),t.closed)return;t.complete()})}(e);if(xd(e))return function RC(e){return Od(Nd(e))}(e)}throw Sd(e)}function Od(e){return new je(t=>{(function FC(e,t){var n,r,o,i;return function bC(e,t,n,r){return new(n||(n=Promise))(function(i,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?i(u.value):function o(i){return i instanceof n?i:new n(function(s){s(i)})}(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}(this,void 0,void 0,function*(){try{for(n=MC(e);!(r=yield n.next()).done;)if(t.next(r.value),t.closed)return}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})})(e,t).catch(n=>t.error(n))})}function fn(e,t,n,r=0,o=!1){const i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Rd(e,t,n=1/0){return _e(t)?Rd((r,o)=>Da((i,s)=>t(r,i,o,s))(dn(e(r,o))),n):("number"==typeof t&&(n=t),un((r,o)=>function PC(e,t,n,r,o,i,s,a){const l=[];let c=0,u=0,d=!1;const h=()=>{d&&!l.length&&!c&&t.complete()},p=w=>c<r?m(w):l.push(w),m=w=>{i&&t.next(w),c++;let I=!1;dn(n(w,u++)).subscribe(new zt(t,A=>{o?.(A),i?p(A):t.next(A)},()=>{I=!0},void 0,()=>{if(I)try{for(c--;l.length&&c<r;){const A=l.shift();s?fn(t,s,()=>m(A)):m(A)}h()}catch(A){t.error(A)}}))};return e.subscribe(new zt(t,p,()=>{d=!0,h()})),()=>{a?.()}}(r,o,e,n)))}const ba=new je(e=>e.complete());function Ea(e){return e[e.length-1]}function Fd(e){return function VC(e){return e&&_e(e.schedule)}(Ea(e))?e.pop():void 0}function Pd(e,t=0){return un((n,r)=>{n.subscribe(new zt(r,o=>fn(r,e,()=>r.next(o),t),()=>fn(r,e,()=>r.complete(),t),o=>fn(r,e,()=>r.error(o),t)))})}function kd(e,t=0){return un((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ld(e,t){if(!e)throw new Error("Iterable cannot be null");return new je(n=>{fn(n,t,()=>{const r=e[Symbol.asyncIterator]();fn(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function fi(e,t){return t?function qC(e,t){if(null!=e){if(Id(e))return function jC(e,t){return dn(e).pipe(kd(t),Pd(t))}(e,t);if(bd(e))return function UC(e,t){return new je(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}(e,t);if(Ed(e))return function $C(e,t){return dn(e).pipe(kd(t),Pd(t))}(e,t);if(Md(e))return Ld(e,t);if(Td(e))return function zC(e,t){return new je(n=>{let r;return fn(n,t,()=>{r=e[Ad](),fn(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){return void n.error(s)}i?n.complete():n.next(o)},0,!0)}),()=>_e(r?.return)&&r.return()})}(e,t);if(xd(e))return function GC(e,t){return Ld(Nd(e),t)}(e,t)}throw Sd(e)}(e,t):dn(e)}function WC(...e){const t=Fd(e),n=function BC(e,t){return"number"==typeof Ea(e)?e.pop():t}(e,1/0),r=e;return r.length?1===r.length?dn(r[0]):function kC(e=1/0){return Rd(Ca,e)}(n)(fi(r,t)):ba}class ZC extends di{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){const{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}}function Vd(e={}){const{connector:t=(()=>new di),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s=null,a=null,l=null,c=0,u=!1,d=!1;const h=()=>{a?.unsubscribe(),a=null},p=()=>{h(),s=l=null,u=d=!1},m=()=>{const w=s;p(),w?.unsubscribe()};return un((w,I)=>{c++,!d&&!u&&h();const A=l=l??t();I.add(()=>{c--,0===c&&!d&&!u&&(a=Ia(m,o))}),A.subscribe(I),s||(s=new ma({next:D=>A.next(D),error:D=>{d=!0,h(),a=Ia(p,n,D),A.error(D)},complete:()=>{u=!0,h(),a=Ia(p,r),A.complete()}}),fi(w).subscribe(s))})(i)}}function Ia(e,t,...n){return!0===t?(e(),null):!1===t?null:t(...n).pipe(function YC(e){return e<=0?()=>ba:un((t,n)=>{let r=0;t.subscribe(new zt(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}(1)).subscribe(()=>e())}function XC(e,t){return e===t}function ie(e){for(let t in e)if(e[t]===ie)return t;throw Error("Could not find renamed property on target object.")}function hi(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ae(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Ae).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function Ma(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const eD=ie({__forward_ref__:ie});function ue(e){return e.__forward_ref__=ue,e.toString=function(){return Ae(this())},e}function V(e){return Sa(e)?e():e}function Sa(e){return"function"==typeof e&&e.hasOwnProperty(eD)&&e.__forward_ref__===ue}function Aa(e){return e&&!!e.\u0275providers}const Hd="https://g.co/ng/security#xss";class S extends Error{constructor(t,n){super(function pi(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function H(e){return"string"==typeof e?e:null==e?"":String(e)}function Ta(e,t){throw new S(-201,!1)}function ft(e,t){null==e&&function P(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function se(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Gt(e){return{providers:e.providers||[],imports:e.imports||[]}}function gi(e){return Bd(e,_i)||Bd(e,jd)}function Bd(e,t){return e.hasOwnProperty(t)?e[t]:null}function mi(e){return e&&(e.hasOwnProperty(Na)||e.hasOwnProperty(aD))?e[Na]:null}const _i=ie({\u0275prov:ie}),Na=ie({\u0275inj:ie}),jd=ie({ngInjectableDef:ie}),aD=ie({ngInjectorDef:ie});var K=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(K||{});let xa;function Ke(e){const t=xa;return xa=e,t}function Ud(e,t,n){const r=gi(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&K.Optional?null:void 0!==t?t:void Ta(Ae(e))}const de=globalThis,Qr={},ka="__NG_DI_FLAG__",vi="ngTempTokenPath",uD=/\n/gm,Gd="__source";let Kn;function hn(e){const t=Kn;return Kn=e,t}function hD(e,t=K.Default){if(void 0===Kn)throw new S(-203,!1);return null===Kn?Ud(e,void 0,t):Kn.get(e,t&K.Optional?null:void 0,t)}function J(e,t=K.Default){return(function $d(){return xa}()||hD)(V(e),t)}function le(e,t=K.Default){return J(e,yi(t))}function yi(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function La(e){const t=[];for(let n=0;n<e.length;n++){const r=V(e[n]);if(Array.isArray(r)){if(0===r.length)throw new S(900,!1);let o,i=K.Default;for(let s=0;s<r.length;s++){const a=r[s],l=pD(a);"number"==typeof l?-1===l?o=a.token:i|=l:o=a}t.push(J(o,i))}else t.push(J(r))}return t}function Yr(e,t){return e[ka]=t,e.prototype[ka]=t,e}function pD(e){return e[ka]}function qt(e){return{toString:e}.toString()}var Ci=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ci||{}),yt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(yt||{});const xt={},te=[],Di=ie({\u0275cmp:ie}),Va=ie({\u0275dir:ie}),Ha=ie({\u0275pipe:ie}),Wd=ie({\u0275mod:ie}),Wt=ie({\u0275fac:ie}),Kr=ie({__NG_ELEMENT_ID__:ie}),Zd=ie({__NG_ENV_ID__:ie});function Qd(e,t,n){let r=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function Ba(e,t,n){let r=0;for(;r<n.length;){const o=n[r];if("number"==typeof o){if(0!==o)break;r++;const i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{const i=o,s=n[++r];Kd(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Yd(e){return 3===e||4===e||6===e}function Kd(e){return 64===e.charCodeAt(0)}function Jr(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const o=t[r];"number"==typeof o?n=o:0===n||Jd(e,n,o,null,-1===n||2===n?t[++r]:null)}}return e}function Jd(e,t,n,r,o){let i=0,s=e.length;if(-1===t)s=-1;else for(;i<e.length;){const a=e[i++];if("number"==typeof a){if(a===t){s=-1;break}if(a>t){s=i-1;break}}}for(;i<e.length;){const a=e[i];if("number"==typeof a)break;if(a===n){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==s&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}const Xd="ng-template";function _D(e,t,n){let r=0,o=!0;for(;r<e.length;){let i=e[r++];if("string"==typeof i&&o){const s=e[r++];if(n&&"class"===i&&-1!==Qd(s.toLowerCase(),t,0))return!0}else{if(1===i){for(;r<e.length&&"string"==typeof(i=e[r++]);)if(i.toLowerCase()===t)return!0;return!1}"number"==typeof i&&(o=!1)}}return!1}function ef(e){return 4===e.type&&e.value!==Xd}function vD(e,t,n){return t===(4!==e.type||n?e.value:Xd)}function yD(e,t,n){let r=4;const o=e.attrs||[],i=function wD(e){for(let t=0;t<e.length;t++)if(Yd(e[t]))return t;return e.length}(o);let s=!1;for(let a=0;a<t.length;a++){const l=t[a];if("number"!=typeof l){if(!s)if(4&r){if(r=2|1&r,""!==l&&!vD(e,l,n)||""===l&&1===t.length){if(Ct(r))return!1;s=!0}}else{const c=8&r?l:t[++a];if(8&r&&null!==e.attrs){if(!_D(e.attrs,c,n)){if(Ct(r))return!1;s=!0}continue}const d=CD(8&r?"class":l,o,ef(e),n);if(-1===d){if(Ct(r))return!1;s=!0;continue}if(""!==c){let h;h=d>i?"":o[d+1].toLowerCase();const p=8&r?h:null;if(p&&-1!==Qd(p,c,0)||2&r&&c!==h){if(Ct(r))return!1;s=!0}}}}else{if(!s&&!Ct(r)&&!Ct(l))return!1;if(s&&Ct(l))continue;s=!1,r=l|1&r}}return Ct(r)||s}function Ct(e){return 0==(1&e)}function CD(e,t,n,r){if(null===t)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){const s=t[o];if(s===e)return o;if(3===s||6===s)i=!0;else{if(1===s||2===s){let a=t[++o];for(;"string"==typeof a;)a=t[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=i?1:2}return-1}return function bD(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function tf(e,t,n=!1){for(let r=0;r<t.length;r++)if(yD(e,t[r],n))return!0;return!1}function nf(e,t){return e?":not("+t.trim()+")":t}function ID(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?o+="."+s:4&r&&(o+=" "+s);else""!==o&&!Ct(s)&&(t+=nf(i,o),o=""),r=s,i=i||!Ct(r);n++}return""!==o&&(t+=nf(i,o)),t}function Zt(e){return qt(()=>{const t=of(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ci.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||yt.Emulated,styles:e.styles||te,_:null,schemas:e.schemas||null,tView:null,id:""};sf(n);const r=e.dependencies;return n.directiveDefs=wi(r,!1),n.pipeDefs=wi(r,!0),n.id=function RD(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function TD(e){return X(e)||Oe(e)}function ND(e){return null!==e}function pn(e){return qt(()=>({type:e.type,bootstrap:e.bootstrap||te,declarations:e.declarations||te,imports:e.imports||te,exports:e.exports||te,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function rf(e,t){if(null==e)return xt;const n={};for(const r in e)if(e.hasOwnProperty(r)){let o=e[r],i=o;Array.isArray(o)&&(i=o[1],o=o[0]),n[o]=r,t&&(t[o]=i)}return n}function j(e){return qt(()=>{const t=of(e);return sf(t),t})}function Je(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function X(e){return e[Di]||null}function Oe(e){return e[Va]||null}function $e(e){return e[Ha]||null}function of(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||xt,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||te,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:rf(e.inputs,t),outputs:rf(e.outputs)}}function sf(e){e.features?.forEach(t=>t(e))}function wi(e,t){if(!e)return null;const n=t?$e:TD;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(ND)}const ye=0,T=1,U=2,ge=3,Dt=4,eo=5,ke=6,Jn=7,be=8,gn=9,Xn=10,B=11,to=12,af=13,er=14,Ee=15,no=16,tr=17,Ot=18,ro=19,lf=20,mn=21,Qt=22,oo=23,io=24,W=25,ja=1,cf=2,Rt=7,nr=9,Re=11;function Xe(e){return Array.isArray(e)&&"object"==typeof e[ja]}function Ue(e){return Array.isArray(e)&&!0===e[ja]}function $a(e){return 0!=(4&e.flags)}function Pn(e){return e.componentOffset>-1}function Ei(e){return 1==(1&e.flags)}function wt(e){return!!e.template}function Ua(e){return 0!=(512&e[U])}function kn(e,t){return e.hasOwnProperty(Wt)?e[Wt]:null}let Fe=null,Ii=!1;function ht(e){const t=Fe;return Fe=e,t}const ff={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function pf(e){if(!ao(e)||e.dirty){if(!e.producerMustRecompute(e)&&!_f(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function mf(e){e.dirty=!0,function gf(e){if(void 0===e.liveConsumerNode)return;const t=Ii;Ii=!0;try{for(const n of e.liveConsumerNode)n.dirty||mf(n)}finally{Ii=t}}(e),e.consumerMarkedDirty?.(e)}function Ga(e){return e&&(e.nextProducerIndex=0),ht(e)}function qa(e,t){if(ht(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(ao(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Mi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function _f(e){rr(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(pf(n),r!==n.version))return!0}return!1}function vf(e){if(rr(e),ao(e))for(let t=0;t<e.producerNode.length;t++)Mi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Mi(e,t){if(function Cf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),rr(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)Mi(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];rr(o),o.producerIndexOfThis[r]=t}}function ao(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function rr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let Df=null;const If=()=>{},qD=(()=>({...ff,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:If}))();class WD{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function Yt(){return Mf}function Mf(e){return e.type.prototype.ngOnChanges&&(e.setInput=QD),ZD}function ZD(){const e=Af(this),t=e?.current;if(t){const n=e.previous;if(n===xt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function QD(e,t,n,r){const o=this.declaredInputs[n],i=Af(e)||function YD(e,t){return e[Sf]=t}(e,{previous:xt,current:null}),s=i.current||(i.current={}),a=i.previous,l=a[o];s[o]=new WD(l&&l.currentValue,t,a===xt),e[r]=t}Yt.ngInherit=!0;const Sf="__ngSimpleChanges__";function Af(e){return e[Sf]||null}const Ft=function(e,t,n){};function fe(e){for(;Array.isArray(e);)e=e[ye];return e}function Si(e,t){return fe(t[e])}function et(e,t){return fe(t[e.index])}function xf(e,t){return e.data[t]}function at(e,t){const n=t[e];return Xe(n)?n:n[ye]}function vn(e,t){return null==t?null:e[t]}function Of(e){e[tr]=0}function nw(e){1024&e[U]||(e[U]|=1024,Ff(e,1))}function Rf(e){1024&e[U]&&(e[U]&=-1025,Ff(e,-1))}function Ff(e,t){let n=e[ge];if(null===n)return;n[eo]+=t;let r=n;for(n=n[ge];null!==n&&(1===t&&1===r[eo]||-1===t&&0===r[eo]);)n[eo]+=t,r=n,n=n[ge]}const k={lFrame:Gf(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Lf(){return k.bindingsEnabled}function E(){return k.lFrame.lView}function ee(){return k.lFrame.tView}function G(e){return k.lFrame.contextLView=e,e[be]}function q(e){return k.lFrame.contextLView=null,e}function Pe(){let e=Vf();for(;null!==e&&64===e.type;)e=e.parent;return e}function Vf(){return k.lFrame.currentTNode}function Pt(e,t){const n=k.lFrame;n.currentTNode=e,n.isParent=t}function Ka(){return k.lFrame.isParent}function Ja(){k.lFrame.isParent=!1}function ze(){const e=k.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function sr(){return k.lFrame.bindingIndex++}function Jt(e){const t=k.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function pw(e,t){const n=k.lFrame;n.bindingIndex=n.bindingRootIndex=e,Xa(t)}function Xa(e){k.lFrame.currentDirectiveIndex=e}function tl(e){k.lFrame.currentQueryIndex=e}function mw(e){const t=e[T];return 2===t.type?t.declTNode:1===t.type?e[ke]:null}function Uf(e,t,n){if(n&K.SkipSelf){let o=t,i=e;for(;!(o=o.parent,null!==o||n&K.Host||(o=mw(i),null===o||(i=i[er],10&o.type))););if(null===o)return!1;t=o,e=i}const r=k.lFrame=zf();return r.currentTNode=t,r.lView=e,!0}function nl(e){const t=zf(),n=e[T];k.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function zf(){const e=k.lFrame,t=null===e?null:e.child;return null===t?Gf(e):t}function Gf(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function qf(){const e=k.lFrame;return k.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Wf=qf;function rl(){const e=qf();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Ge(){return k.lFrame.selectedIndex}function Ln(e){k.lFrame.selectedIndex=e}function ve(){const e=k.lFrame;return xf(e.tView,e.selectedIndex)}let Yf=!0;function Ai(){return Yf}function yn(e){Yf=e}function Ti(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),null!=u&&(e.destroyHooks??=[]).push(n,u)}}function Ni(e,t,n){Kf(e,t,3,n)}function xi(e,t,n,r){(3&e[U])===n&&Kf(e,t,n,r)}function ol(e,t){let n=e[U];(3&n)===t&&(n&=8191,n+=1,e[U]=n)}function Kf(e,t,n,r){const i=r??-1,s=t.length-1;let a=0;for(let l=void 0!==r?65535&e[tr]:0;l<s;l++)if("number"==typeof t[l+1]){if(a=t[l],null!=r&&a>=r)break}else t[l]<0&&(e[tr]+=65536),(a<i||-1==i)&&(bw(e,n,t,l),e[tr]=(**********&e[tr])+l+2),l++}function Jf(e,t){Ft(4,e,t);const n=ht(null);try{t.call(e)}finally{ht(n),Ft(5,e,t)}}function bw(e,t,n,r){const o=n[r]<0,i=n[r+1],a=e[o?-n[r]:n[r]];o?e[U]>>13<e[tr]>>16&&(3&e[U])===t&&(e[U]+=8192,Jf(a,i)):Jf(a,i)}const ar=-1;class co{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function sl(e){return e!==ar}function uo(e){return 32767&e}function fo(e,t){let n=function Sw(e){return e>>16}(e),r=t;for(;n>0;)r=r[er],n--;return r}let al=!0;function Oi(e){const t=al;return al=e,t}const Xf=255,eh=5;let Aw=0;const kt={};function Ri(e,t){const n=th(e,t);if(-1!==n)return n;const r=t[T];r.firstCreatePass&&(e.injectorIndex=t.length,ll(r.data,e),ll(t,null),ll(r.blueprint,null));const o=Fi(e,t),i=e.injectorIndex;if(sl(o)){const s=uo(o),a=fo(o,t),l=a[T].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|l[s+c]}return t[i+8]=o,i}function ll(e,t){e.push(0,0,0,0,0,0,0,0,t)}function th(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function Fi(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;null!==o;){if(r=lh(o),null===r)return ar;if(n++,o=o[er],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return ar}function cl(e,t,n){!function Tw(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(Kr)&&(r=n[Kr]),null==r&&(r=n[Kr]=Aw++);const o=r&Xf;t.data[e+(o>>eh)]|=1<<o}(e,t,n)}function nh(e,t,n){if(n&K.Optional||void 0!==e)return e;Ta()}function rh(e,t,n,r){if(n&K.Optional&&void 0===r&&(r=null),!(n&(K.Self|K.Host))){const o=e[gn],i=Ke(void 0);try{return o?o.get(t,r,n&K.Optional):Ud(t,r,n&K.Optional)}finally{Ke(i)}}return nh(r,0,n)}function oh(e,t,n,r=K.Default,o){if(null!==e){if(2048&t[U]&&!(r&K.Self)){const s=function Pw(e,t,n,r,o){let i=e,s=t;for(;null!==i&&null!==s&&2048&s[U]&&!(512&s[U]);){const a=ih(i,s,n,r|K.Self,kt);if(a!==kt)return a;let l=i.parent;if(!l){const c=s[lf];if(c){const u=c.get(n,kt,r);if(u!==kt)return u}l=lh(s),s=s[er]}i=l}return o}(e,t,n,r,kt);if(s!==kt)return s}const i=ih(e,t,n,r,kt);if(i!==kt)return i}return rh(t,n,r,o)}function ih(e,t,n,r,o){const i=function Ow(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(Kr)?e[Kr]:void 0;return"number"==typeof t?t>=0?t&Xf:Fw:t}(n);if("function"==typeof i){if(!Uf(t,e,r))return r&K.Host?nh(o,0,r):rh(t,n,r,o);try{let s;if(s=i(r),null!=s||r&K.Optional)return s;Ta()}finally{Wf()}}else if("number"==typeof i){let s=null,a=th(e,t),l=ar,c=r&K.Host?t[Ee][ke]:null;for((-1===a||r&K.SkipSelf)&&(l=-1===a?Fi(e,t):t[a+8],l!==ar&&ah(r,!1)?(s=t[T],a=uo(l),t=fo(l,t)):a=-1);-1!==a;){const u=t[T];if(sh(i,a,u.data)){const d=xw(a,t,n,s,r,c);if(d!==kt)return d}l=t[a+8],l!==ar&&ah(r,t[T].data[a+8]===c)&&sh(i,a,t)?(s=u,a=uo(l),t=fo(l,t)):a=-1}}return o}function xw(e,t,n,r,o,i){const s=t[T],a=s.data[e+8],u=function Pi(e,t,n,r,o){const i=e.providerIndexes,s=t.data,a=1048575&i,l=e.directiveStart,u=i>>20,h=o?a+u:e.directiveEnd;for(let p=r?a:a+u;p<h;p++){const m=s[p];if(p<l&&n===m||p>=l&&m.type===n)return p}if(o){const p=s[l];if(p&&wt(p)&&p.type===n)return l}return null}(a,s,n,null==r?Pn(a)&&al:r!=s&&0!=(3&a.type),o&K.Host&&i===a);return null!==u?Vn(t,s,u,a):kt}function Vn(e,t,n,r){let o=e[n];const i=t.data;if(function Ew(e){return e instanceof co}(o)){const s=o;s.resolving&&function tD(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new S(-200,`Circular dependency in DI detected for ${e}${n}`)}(function oe(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():H(e)}(i[n]));const a=Oi(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?Ke(s.injectImpl):null;Uf(e,r,K.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&function ww(e,t,n){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){const s=Mf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}(n,i[n],t)}finally{null!==c&&Ke(c),Oi(a),s.resolving=!1,Wf()}}return o}function sh(e,t,n){return!!(n[t+(e>>eh)]&1<<e)}function ah(e,t){return!(e&K.Self||e&K.Host&&t)}class qe{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return oh(this._tNode,this._lView,t,yi(r),n)}}function Fw(){return new qe(Pe(),E())}function Le(e){return qt(()=>{const t=e.prototype.constructor,n=t[Wt]||ul(t),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const i=o[Wt]||ul(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ul(e){return Sa(e)?()=>{const t=ul(V(e));return t&&t()}:kn(e)}function lh(e){const t=e[T],n=t.type;return 2===n?t.declTNode:1===n?e[ke]:null}const cr="__parameters__";function dr(e,t,n){return qt(()=>{const r=function fl(e){return function(...n){if(e){const r=e(...n);for(const o in r)this[o]=r[o]}}}(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;const s=new o(...i);return a.annotation=s,a;function a(l,c,u){const d=l.hasOwnProperty(cr)?l[cr]:Object.defineProperty(l,cr,{value:[]})[cr];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function hr(e,t){e.forEach(n=>Array.isArray(n)?hr(n,t):t(n))}function uh(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ki(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function lt(e,t,n){let r=pr(e,t);return r>=0?e[1|r]=n:(r=~r,function $w(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function hl(e,t){const n=pr(e,t);if(n>=0)return e[1|n]}function pr(e,t){return function dh(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){const i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}(e,t,1)}const gl=Yr(dr("Optional"),8),ml=Yr(dr("SkipSelf"),4);function $i(e){return 128==(128&e.flags)}var Cn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Cn||{});const cb=/^>|^->|<!--|-->|--!>|<!-$/g,ub=/(<|>)/g,db="\u200b$1\u200b";const Cl=new Map;let fb=0;const wl="__ngContext__";function Ve(e,t){Xe(t)?(e[wl]=t[ro],function pb(e){Cl.set(e[ro],e)}(t)):e[wl]=t}let bl;function El(e,t){return bl(e,t)}function vo(e){const t=e[ge];return Ue(t)?t[ge]:t}function xh(e){return Rh(e[to])}function Oh(e){return Rh(e[Dt])}function Rh(e){for(;null!==e&&!Ue(e);)e=e[Dt];return e}function _r(e,t,n,r,o){if(null!=r){let i,s=!1;Ue(r)?i=r:Xe(r)&&(s=!0,r=r[ye]);const a=fe(r);0===e&&null!==n?null==o?Lh(t,n,a):Hn(t,n,a,o||null,!0):1===e&&null!==n?Hn(t,n,a,o||null,!0):2===e?function Qi(e,t,n){const r=Wi(e,t);r&&function Rb(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,a,s):3===e&&t.destroyNode(a),null!=i&&function kb(e,t,n,r,o){const i=n[Rt];i!==fe(n)&&_r(t,e,r,i,o);for(let a=Re;a<n.length;a++){const l=n[a];Co(l[T],l,e,t,r,i)}}(t,e,i,n,o)}}function Il(e,t){return e.createComment(function bh(e){return e.replace(cb,t=>t.replace(ub,db))}(t))}function Gi(e,t,n){return e.createElement(t,n)}function Ph(e,t){const n=e[nr],r=n.indexOf(t);Rf(t),n.splice(r,1)}function qi(e,t){if(e.length<=Re)return;const n=Re+t,r=e[n];if(r){const o=r[no];null!==o&&o!==e&&Ph(o,r),t>0&&(e[n-1][Dt]=r[Dt]);const i=ki(e,Re+t);!function Ib(e,t){Co(e,t,t[B],2,null,null),t[ye]=null,t[ke]=null}(r[T],r);const s=i[Ot];null!==s&&s.detachView(i[T]),r[ge]=null,r[Dt]=null,r[U]&=-129}return r}function Ml(e,t){if(!(256&t[U])){const n=t[B];t[oo]&&vf(t[oo]),t[io]&&vf(t[io]),n.destroyNode&&Co(e,t,n,3,null,null),function Ab(e){let t=e[to];if(!t)return Sl(e[T],e);for(;t;){let n=null;if(Xe(t))n=t[to];else{const r=t[Re];r&&(n=r)}if(!n){for(;t&&!t[Dt]&&t!==e;)Xe(t)&&Sl(t[T],t),t=t[ge];null===t&&(t=e),Xe(t)&&Sl(t[T],t),n=t&&t[Dt]}t=n}}(t)}}function Sl(e,t){if(!(256&t[U])){t[U]&=-129,t[U]|=256,function Ob(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const o=t[n[r]];if(!(o instanceof co)){const i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){const a=o[i[s]],l=i[s+1];Ft(4,a,l);try{l.call(a)}finally{Ft(5,a,l)}}else{Ft(4,o,i);try{i.call(o)}finally{Ft(5,o,i)}}}}}(e,t),function xb(e,t){const n=e.cleanup,r=t[Jn];if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else n[i].call(r[n[i+1]]);null!==r&&(t[Jn]=null);const o=t[mn];if(null!==o){t[mn]=null;for(let i=0;i<o.length;i++)(0,o[i])()}}(e,t),1===t[T].type&&t[B].destroy();const n=t[no];if(null!==n&&Ue(t[ge])){n!==t[ge]&&Ph(n,t);const r=t[Ot];null!==r&&r.detachView(e)}!function gb(e){Cl.delete(e[ro])}(t)}}function Al(e,t,n){return function kh(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[ye];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:i}=e.data[r.directiveStart+o];if(i===yt.None||i===yt.Emulated)return null}return et(r,n)}}(e,t.parent,n)}function Hn(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Lh(e,t,n){e.appendChild(t,n)}function Vh(e,t,n,r,o){null!==r?Hn(e,t,n,r,o):Lh(e,t,n)}function Wi(e,t){return e.parentNode(t)}let Tl,Rl,jh=function Bh(e,t,n){return 40&e.type?et(e,n):null};function Zi(e,t,n,r){const o=Al(e,r,t),i=t[B],a=function Hh(e,t,n){return jh(e,t,n)}(r.parent||t[ke],r,t);if(null!=o)if(Array.isArray(n))for(let l=0;l<n.length;l++)Vh(i,o,n[l],a,!1);else Vh(i,o,n,a,!1);void 0!==Tl&&Tl(i,r,t,n,o)}function yo(e,t){if(null!==t){const n=t.type;if(3&n)return et(t,e);if(4&n)return Nl(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return yo(e,r);{const o=e[t.index];return Ue(o)?Nl(-1,o):fe(o)}}if(32&n)return El(t,e)()||fe(e[t.index]);{const r=Uh(e,t);return null!==r?Array.isArray(r)?r[0]:yo(vo(e[Ee]),r):yo(e,t.next)}}return null}function Uh(e,t){return null!==t?e[Ee][ke].projection[t.projection]:null}function Nl(e,t){const n=Re+e+1;if(n<t.length){const r=t[n],o=r[T].firstChild;if(null!==o)return yo(r,o)}return t[Rt]}function xl(e,t,n,r,o,i,s){for(;null!=n;){const a=r[n.index],l=n.type;if(s&&0===t&&(a&&Ve(fe(a),r),n.flags|=2),32!=(32&n.flags))if(8&l)xl(e,t,n.child,r,o,i,!1),_r(t,e,o,a,i);else if(32&l){const c=El(n,r);let u;for(;u=c();)_r(t,e,o,u,i);_r(t,e,o,a,i)}else 16&l?Gh(e,t,r,n,o,i):_r(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Co(e,t,n,r,o,i){xl(n,r,e.firstChild,t,o,i,!1)}function Gh(e,t,n,r,o,i){const s=n[Ee],l=s[ke].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)_r(t,e,o,l[c],i);else{let c=l;const u=s[ge];$i(r)&&(c.flags|=128),xl(e,t,c,u,o,i,!0)}}function qh(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Wh(e,t,n){const{mergedAttrs:r,classes:o,styles:i}=n;null!==r&&Ba(e,t,r),null!==o&&qh(e,t,o),null!==i&&function Vb(e,t,n){e.setAttribute(t,"style",n)}(e,t,i)}class Kh{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Hd})`}}function Dn(e){return e instanceof Kh?e.changingThisBreaksApplicationSecurity:e}const Jb=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Cr=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Cr||{});function wn(e){const t=function bo(){const e=E();return e&&e[Xn].sanitizer}();return t?t.sanitize(Cr.URL,e)||"":function Do(e,t){const n=function Zb(e){return e instanceof Kh&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Hd})`)}return n===t}(e,"URL")?Dn(e):function Pl(e){return(e=String(e)).match(Jb)?e:"unsafe:"+e}(H(e))}class R{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=se({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const Xi=new R("ENVIRONMENT_INITIALIZER"),ip=new R("INJECTOR",-1),sp=new R("INJECTOR_DEF_TYPES");class Hl{get(t,n=Qr){if(n===Qr){const r=new Error(`NullInjectorError: No provider for ${Ae(t)}!`);throw r.name="NullInjectorError",r}return n}}function fE(...e){return{\u0275providers:lp(0,e),\u0275fromNgModule:!0}}function lp(e,...t){const n=[],r=new Set;let o;const i=s=>{n.push(s)};return hr(t,s=>{const a=s;es(a,i,[],r)&&(o||=[],o.push(a))}),void 0!==o&&cp(o,i),n}function cp(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:o}=e[n];Bl(o,i=>{t(i,r)})}}function es(e,t,n,r){if(!(e=V(e)))return!1;let o=null,i=mi(e);const s=!i&&X(e);if(i||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(i=mi(l),!i)return!1;o=l}const a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)es(c,t,n,r)}}else{if(!i)return!1;{if(null!=i.imports&&!a){let c;r.add(o);try{hr(i.imports,u=>{es(u,t,n,r)&&(c||=[],c.push(u))})}finally{}void 0!==c&&cp(c,t)}if(!a){const c=kn(o)||(()=>new o);t({provide:o,useFactory:c,deps:te},o),t({provide:sp,useValue:o,multi:!0},o),t({provide:Xi,useValue:()=>J(o),multi:!0},o)}const l=i.providers;if(null!=l&&!a){const c=e;Bl(l,u=>{t(u,c)})}}}return o!==e&&void 0!==e.providers}function Bl(e,t){for(let n of e)Aa(n)&&(n=n.\u0275providers),Array.isArray(n)?Bl(n,t):t(n)}const hE=ie({provide:String,useValue:ie});function jl(e){return null!==e&&"object"==typeof e&&hE in e}function Bn(e){return"function"==typeof e}const $l=new R("Set Injector scope."),ts={},gE={};let Ul;function ns(){return void 0===Ul&&(Ul=new Hl),Ul}class en{}class rs extends en{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Gl(t,s=>this.processProvider(s)),this.records.set(ip,Dr(void 0,this)),o.has("environment")&&this.records.set(en,Dr(void 0,this));const i=this.records.get($l);null!=i&&"string"==typeof i.value&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(sp.multi,te,K.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=hn(this),r=Ke(void 0);try{return t()}finally{hn(n),Ke(r)}}get(t,n=Qr,r=K.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Zd))return t[Zd](this);r=yi(r);const i=hn(this),s=Ke(void 0);try{if(!(r&K.SkipSelf)){let l=this.records.get(t);if(void 0===l){const c=function CE(e){return"function"==typeof e||"object"==typeof e&&e instanceof R}(t)&&gi(t);l=c&&this.injectableDefInScope(c)?Dr(zl(t),ts):null,this.records.set(t,l)}if(null!=l)return this.hydrate(t,l)}return(r&K.Self?ns():this.parent).get(t,n=r&K.Optional&&n===Qr?null:n)}catch(a){if("NullInjectorError"===a.name){if((a[vi]=a[vi]||[]).unshift(Ae(t)),i)throw a;return function gD(e,t,n,r){const o=e[vi];throw t[Gd]&&o.unshift(t[Gd]),e.message=function mD(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=Ae(t);if(Array.isArray(t))o=t.map(Ae).join(" -> ");else if("object"==typeof t){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+("string"==typeof a?JSON.stringify(a):Ae(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(uD,"\n  ")}`}("\n"+e.message,o,n,r),e.ngTokenPath=o,e[vi]=null,e}(a,t,"R3InjectorError",this.source)}throw a}finally{Ke(s),hn(i)}}resolveInjectorInitializers(){const t=hn(this),n=Ke(void 0);try{const o=this.get(Xi.multi,te,K.Self);for(const i of o)i()}finally{hn(t),Ke(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(Ae(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new S(205,!1)}processProvider(t){let n=Bn(t=V(t))?t:V(t&&t.provide);const r=function _E(e){return jl(e)?Dr(void 0,e.useValue):Dr(fp(e),ts)}(t);if(Bn(t)||!0!==t.multi)this.records.get(n);else{let o=this.records.get(n);o||(o=Dr(void 0,ts,!0),o.factory=()=>La(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===ts&&(n.value=gE,n.value=n.factory()),"object"==typeof n.value&&n.value&&function yE(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=V(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function zl(e){const t=gi(e),n=null!==t?t.factory:kn(e);if(null!==n)return n;if(e instanceof R)throw new S(204,!1);if(e instanceof Function)return function mE(e){const t=e.length;if(t>0)throw function go(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new S(204,!1);const n=function sD(e){return e&&(e[_i]||e[jd])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new S(204,!1)}function fp(e,t,n){let r;if(Bn(e)){const o=V(e);return kn(o)||zl(o)}if(jl(e))r=()=>V(e.useValue);else if(function dp(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...La(e.deps||[]));else if(function up(e){return!(!e||!e.useExisting)}(e))r=()=>J(V(e.useExisting));else{const o=V(e&&(e.useClass||e.provide));if(!function vE(e){return!!e.deps}(e))return kn(o)||zl(o);r=()=>new o(...La(e.deps))}return r}function Dr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Gl(e,t){for(const n of e)Array.isArray(n)?Gl(n,t):n&&Aa(n)?Gl(n.\u0275providers,t):t(n)}const os=new R("AppId",{providedIn:"root",factory:()=>DE}),DE="ng",hp=new R("Platform Initializer"),wr=new R("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),pp=new R("CSP nonce",{providedIn:"root",factory:()=>function yr(){if(void 0!==Rl)return Rl;if(typeof document<"u")return document;throw new S(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let gp=(e,t,n)=>null;function Xl(e,t,n=!1){return gp(e,t,n)}class xE{}class vp{}class RE{resolveComponentFactory(t){throw function OE(e){const t=Error(`No component factory found for ${Ae(e)}.`);return t.ngComponent=e,t}(t)}}let us=(()=>{class t{}return t.NULL=new RE,t})();function FE(){return Ir(Pe(),E())}function Ir(e,t){return new bt(et(e,t))}let bt=(()=>{class t{constructor(r){this.nativeElement=r}}return t.__NG_ELEMENT_ID__=FE,t})();class Cp{}let jn=(()=>{class t{constructor(){this.destroyNode=null}}return t.__NG_ELEMENT_ID__=()=>function kE(){const e=E(),n=at(Pe().index,e);return(Xe(n)?n:e)[B]}(),t})(),LE=(()=>{var e;class t{}return(e=t).\u0275prov=se({token:e,providedIn:"root",factory:()=>null}),t})();class ds{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const VE=new ds("16.2.8"),nc={};function Ep(e,t=null,n=null,r){const o=Ip(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ip(e,t=null,n=null,r,o=new Set){const i=[n||te,fE(e)];return r=r||("object"==typeof e?void 0:Ae(e)),new rs(i,t||ns(),r||null,o)}let Et=(()=>{var e;class t{static create(r,o){if(Array.isArray(r))return Ep({name:""},o,r,"");{const i=r.name??"";return Ep({name:i},r.parent,r.providers,i)}}}return(e=t).THROW_IF_NOT_FOUND=Qr,e.NULL=new Hl,e.\u0275prov=se({token:e,providedIn:"any",factory:()=>J(ip)}),e.__NG_ELEMENT_ID__=-1,t})();function oc(e){return e.ngOriginalError}class tn{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&oc(t);for(;n&&oc(n);)n=oc(n);return n||null}}function sc(e){return t=>{setTimeout(e,void 0,t)}}const Ie=class GE extends di{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&"object"==typeof t){const l=t;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=sc(i),o&&(o=sc(o)),s&&(s=sc(s)));const a=super.subscribe({next:o,error:i,complete:s});return t instanceof Nt&&t.add(a),a}};function Sp(...e){}class Ce{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ie(!1),this.onMicrotaskEmpty=new Ie(!1),this.onStable=new Ie(!1),this.onError=new Ie(!1),typeof Zone>"u")throw new S(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function qE(){const e="function"==typeof de.requestAnimationFrame;let t=de[e?"requestAnimationFrame":"setTimeout"],n=de[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function QE(e){const t=()=>{!function ZE(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(de,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,lc(e),e.isCheckStableRunning=!0,ac(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),lc(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(function KE(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(a))return n.invokeTask(o,i,s,a);try{return Ap(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||e.shouldCoalesceRunChangeDetection)&&t(),Tp(e)}},onInvoke:(n,r,o,i,s,a,l)=>{try{return Ap(e),n.invoke(o,i,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&t(),Tp(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&("microTask"==i.change?(e._hasPendingMicrotasks=i.microTask,lc(e),ac(e)):"macroTask"==i.change&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Ce.isInAngularZone())throw new S(909,!1)}static assertNotInAngularZone(){if(Ce.isInAngularZone())throw new S(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){const i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,WE,Sp,Sp);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const WE={};function ac(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function lc(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function Ap(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Tp(e){e._nesting--,ac(e)}class YE{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Ie,this.onMicrotaskEmpty=new Ie,this.onStable=new Ie,this.onError=new Ie}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}}const Np=new R("",{providedIn:"root",factory:xp});function xp(){const e=le(Ce);let t=!0;return WC(new je(o=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{o.next(t),o.complete()})}),new je(o=>{let i;e.runOutsideAngular(()=>{i=e.onStable.subscribe(()=>{Ce.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,o.next(!0))})})});const s=e.onUnstable.subscribe(()=>{Ce.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{o.next(!1)}))});return()=>{i.unsubscribe(),s.unsubscribe()}}).pipe(Vd()))}function cc(e){return e.ownerDocument.defaultView}let uc=(()=>{var e;class t{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}}return(e=t).\u0275prov=se({token:e,providedIn:"root",factory:()=>new e}),t})();function Mo(e){for(;e;){e[U]|=64;const t=vo(e);if(Ua(e)&&!t)return e;e=t}return null}const kp=new R("",{providedIn:"root",factory:()=>!1});let hs=null;function Bp(e,t){return e[t]??Up()}function jp(e,t){const n=Up();n.producerNode?.length&&(e[t]=hs,n.lView=e,hs=$p())}const sI={...ff,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Mo(e.lView)},lView:null};function $p(){return Object.create(sI)}function Up(){return hs??=$p(),hs}const $={};function f(e){zp(ee(),E(),Ge()+e,!1)}function zp(e,t,n,r){if(!r)if(3==(3&t[U])){const i=e.preOrderCheckHooks;null!==i&&Ni(t,i,n)}else{const i=e.preOrderHooks;null!==i&&xi(t,i,0,n)}Ln(n)}function M(e,t=K.Default){const n=E();return null===n?J(e,t):oh(Pe(),n,V(e),t)}function ps(e,t,n,r,o,i,s,a,l,c,u){const d=t.blueprint.slice();return d[ye]=o,d[U]=140|r,(null!==c||e&&2048&e[U])&&(d[U]|=2048),Of(d),d[ge]=d[er]=e,d[be]=n,d[Xn]=s||e&&e[Xn],d[B]=a||e&&e[B],d[gn]=l||e&&e[gn]||null,d[ke]=i,d[ro]=function hb(){return fb++}(),d[Qt]=u,d[lf]=c,d[Ee]=2==t.type?e[Ee]:d,d}function Ar(e,t,n,r,o){let i=e.data[t];if(null===i)i=function dc(e,t,n,r,o){const i=Vf(),s=Ka(),l=e.data[t]=function gI(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return function ir(){return null!==k.skipHydrationRootTNode}()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?i:i&&i.parent,n,t,r,o);return null===e.firstChild&&(e.firstChild=l),null!==i&&(s?null==i.child&&null!==l.parent&&(i.child=l):null===i.next&&(i.next=l,l.prev=i)),l}(e,t,n,r,o),function hw(){return k.lFrame.inI18n}()&&(i.flags|=32);else if(64&i.type){i.type=n,i.value=r,i.attrs=o;const s=function lo(){const e=k.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();i.injectorIndex=null===s?-1:s.injectorIndex}return Pt(i,!0),i}function So(e,t,n,r){if(0===n)return-1;const o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Gp(e,t,n,r,o){const i=Bp(t,oo),s=Ge(),a=2&r;try{Ln(-1),a&&t.length>W&&zp(e,t,W,!1),Ft(a?2:0,o);const c=a?i:null,u=Ga(c);try{null!==c&&(c.dirty=!1),n(r,o)}finally{qa(c,u)}}finally{a&&null===t[oo]&&jp(t,oo),Ln(s),Ft(a?3:1,o)}}function fc(e,t,n){if($a(t)){const r=ht(null);try{const i=t.directiveEnd;for(let s=t.directiveStart;s<i;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,n[s],s)}}finally{ht(r)}}}function hc(e,t,n){Lf()&&(function wI(e,t,n,r){const o=n.directiveStart,i=n.directiveEnd;Pn(n)&&function TI(e,t,n){const r=et(t,e),o=qp(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const a=gs(e,ps(e,o,null,s,r,t,null,e[Xn].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=a}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Ri(n,t),Ve(r,t);const s=n.initialInputs;for(let a=o;a<i;a++){const l=e.data[a],c=Vn(t,e,a,n);Ve(c,t),null!==s&&NI(0,a-o,c,l,0,s),wt(l)&&(at(n.index,t)[be]=Vn(t,e,a,n))}}(e,t,n,et(n,t)),64==(64&n.flags)&&Kp(e,t,n))}function pc(e,t,n=et){const r=t.localNames;if(null!==r){let o=t.index+1;for(let i=0;i<r.length;i+=2){const s=r[i+1],a=-1===s?n(t,e):e[s];e[o++]=a}}}function qp(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=gc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function gc(e,t,n,r,o,i,s,a,l,c,u){const d=W+r,h=d+o,p=function cI(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:$);return n}(d,h),m="function"==typeof c?c():c;return p[T]={type:e,blueprint:p,template:n,queries:null,viewQuery:a,declTNode:t,data:p.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:u}}let Wp=e=>null;function Zp(e,t,n,r){for(let o in e)if(e.hasOwnProperty(o)){n=null===n?{}:n;const i=e[o];null===r?Qp(n,t,o,i):r.hasOwnProperty(o)&&Qp(n,t,r[o],i)}return n}function Qp(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function ct(e,t,n,r,o,i,s,a){const l=et(t,n);let u,c=t.inputs;!a&&null!=c&&(u=c[r])?(Cc(e,n,u,r,o),Pn(t)&&function vI(e,t){const n=at(t,e);16&n[U]||(n[U]|=64)}(n,t.index)):3&t.type&&(r=function _I(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=s?s(o,t.value||"",r):o,i.setProperty(l,r,o))}function mc(e,t,n,r){if(Lf()){const o=null===r?null:{"":-1},i=function EI(e,t){const n=e.directiveRegistry;let r=null,o=null;if(n)for(let i=0;i<n.length;i++){const s=n[i];if(tf(t,s.selectors,!1))if(r||(r=[]),wt(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s),_c(e,t,a.length)}else r.unshift(s),_c(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return null===r?null:[r,o]}(e,n);let s,a;null===i?s=a=null:[s,a]=i,null!==s&&Yp(e,t,n,s,o,a),o&&function II(e,t,n){if(t){const r=e.localNames=[];for(let o=0;o<t.length;o+=2){const i=n[t[o+1]];if(null==i)throw new S(-301,!1);r.push(t[o],i)}}}(n,r,o)}n.mergedAttrs=Jr(n.mergedAttrs,n.attrs)}function Yp(e,t,n,r,o,i){for(let c=0;c<r.length;c++)cl(Ri(n,t),e,r[c].type);!function SI(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let c=0;c<r.length;c++){const u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=So(e,t,r.length,null);for(let c=0;c<r.length;c++){const u=r[c];n.mergedAttrs=Jr(n.mergedAttrs,u.hostAttrs),AI(e,n,t,l,u),MI(l,u,o),null!==u.contentQueries&&(n.flags|=4),(null!==u.hostBindings||null!==u.hostAttrs||0!==u.hostVars)&&(n.flags|=64);const d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}!function mI(e,t,n){const o=t.directiveEnd,i=e.data,s=t.attrs,a=[];let l=null,c=null;for(let u=t.directiveStart;u<o;u++){const d=i[u],h=n?n.get(d):null,m=h?h.outputs:null;l=Zp(d.inputs,u,l,h?h.inputs:null),c=Zp(d.outputs,u,c,m);const w=null===l||null===s||ef(t)?null:xI(l,u,s);a.push(w)}null!==l&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}(e,n,i)}function Kp(e,t,n){const r=n.directiveStart,o=n.directiveEnd,i=n.index,s=function gw(){return k.lFrame.currentDirectiveIndex}();try{Ln(i);for(let a=r;a<o;a++){const l=e.data[a],c=t[a];Xa(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&bI(l,c)}}finally{Ln(-1),Xa(s)}}function bI(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function _c(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function MI(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;wt(t)&&(n[""]=e)}}function AI(e,t,n,r,o){e.data[r]=o;const i=o.factory||(o.factory=kn(o.type)),s=new co(i,wt(o),M);e.blueprint[r]=s,n[r]=s,function CI(e,t,n,r,o){const i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~t.index;(function DI(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=a&&s.push(a),s.push(n,r,i)}}(e,t,r,So(e,n,o.hostVars,$),o)}function Lt(e,t,n,r,o,i){const s=et(e,t);!function vc(e,t,n,r,o,i,s){if(null==i)e.removeAttribute(t,o,n);else{const a=null==s?H(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}(t[B],s,i,e.value,n,r,o)}function NI(e,t,n,r,o,i){const s=i[t];if(null!==s)for(let a=0;a<s.length;)Jp(r,n,s[a++],s[a++],s[a++])}function Jp(e,t,n,r,o){const i=ht(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(o=s[r].call(t,o)),null!==e.setInput?e.setInput(t,o,n,r):t[r]=o}finally{ht(i)}}function xI(e,t,n){let r=null,o=0;for(;o<n.length;){const i=n[o];if(0!==i)if(5!==i){if("number"==typeof i)break;if(e.hasOwnProperty(i)){null===r&&(r=[]);const s=e[i];for(let a=0;a<s.length;a+=2)if(s[a]===t){r.push(i,s[a+1],n[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function Xp(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function eg(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const i=n[r+1];if(-1!==i){const s=e.data[i];tl(n[r]),s.contentQueries(2,t[i],i)}}}function gs(e,t){return e[to]?e[af][Dt]=t:e[to]=t,e[af]=t,t}function yc(e,t,n){tl(0);const r=ht(null);try{t(e,n)}finally{ht(r)}}function og(e,t){const n=e[gn],r=n?n.get(tn,null):null;r&&r.handleError(t)}function Cc(e,t,n,r,o){for(let i=0;i<n.length;){const s=n[i++],a=n[i++];Jp(e.data[s],t[s],r,a,o)}}function OI(e,t){const n=at(t,e),r=n[T];!function RI(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const o=n[ye];null!==o&&null===n[Qt]&&(n[Qt]=Xl(o,n[gn])),Dc(r,n,n[be])}function Dc(e,t,n){nl(t);try{const r=e.viewQuery;null!==r&&yc(1,r,n);const o=e.template;null!==o&&Gp(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&eg(e,t),e.staticViewQueries&&yc(2,e.viewQuery,n);const i=e.components;null!==i&&function FI(e,t){for(let n=0;n<t.length;n++)OI(e,t[n])}(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[U]&=-5,rl()}}let ig=(()=>{var e;class t{constructor(){this.all=new Set,this.queue=new Map}create(r,o,i){const s=typeof Zone>"u"?null:Zone.current,a=function GD(e,t,n){const r=Object.create(qD);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const o=s=>{r.cleanupFn=s};return r.ref={notify:()=>mf(r),run:()=>{if(r.dirty=!1,r.hasRun&&!_f(r))return;r.hasRun=!0;const s=Ga(r);try{r.cleanupFn(),r.cleanupFn=If,r.fn(o)}finally{qa(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(r,u=>{this.all.has(u)&&this.queue.set(u,s)},i);let l;this.all.add(a),a.notify();const c=()=>{a.cleanup(),l?.(),this.all.delete(a),this.queue.delete(a)};return l=o?.onDestroy(c),{destroy:c}}flush(){if(0!==this.queue.size)for(const[r,o]of this.queue)this.queue.delete(r),o?o.run(()=>r.run()):r.run()}get isQueueEmpty(){return 0===this.queue.size}}return(e=t).\u0275prov=se({token:e,providedIn:"root",factory:()=>new e}),t})();function ms(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?i=a:1==i?o=Ma(o,a):2==i&&(r=Ma(r,a+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Ao(e,t,n,r,o=!1){for(;null!==n;){const i=t[n.index];null!==i&&r.push(fe(i)),Ue(i)&&sg(i,r);const s=n.type;if(8&s)Ao(e,t,n.child,r);else if(32&s){const a=El(n,t);let l;for(;l=a();)r.push(l)}else if(16&s){const a=Uh(t,n);if(Array.isArray(a))r.push(...a);else{const l=vo(t[Ee]);Ao(l[T],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function sg(e,t){for(let n=Re;n<e.length;n++){const r=e[n],o=r[T].firstChild;null!==o&&Ao(r[T],r,o,t)}e[Rt]!==e[ye]&&t.push(e[Rt])}function _s(e,t,n,r=!0){const o=t[Xn],i=o.rendererFactory,s=o.afterRenderEventManager;i.begin?.(),s?.begin();try{ag(e,t,e.template,n)}catch(l){throw r&&og(t,l),l}finally{i.end?.(),o.effectManager?.flush(),s?.end()}}function ag(e,t,n,r){const o=t[U];if(256!=(256&o)){t[Xn].effectManager?.flush(),nl(t);try{Of(t),function Bf(e){return k.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&Gp(e,t,n,2,r);const s=3==(3&o);if(s){const c=e.preOrderCheckHooks;null!==c&&Ni(t,c,null)}else{const c=e.preOrderHooks;null!==c&&xi(t,c,0,null),ol(t,0)}if(function LI(e){for(let t=xh(e);null!==t;t=Oh(t)){if(!t[cf])continue;const n=t[nr];for(let r=0;r<n.length;r++){nw(n[r])}}}(t),lg(t,2),null!==e.contentQueries&&eg(e,t),s){const c=e.contentCheckHooks;null!==c&&Ni(t,c)}else{const c=e.contentHooks;null!==c&&xi(t,c,1),ol(t,1)}!function lI(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=Bp(t,io);try{for(let o=0;o<n.length;o++){const i=n[o];if(i<0)Ln(~i);else{const s=i,a=n[++o],l=n[++o];pw(a,s),r.dirty=!1;const c=Ga(r);try{l(2,t[s])}finally{qa(r,c)}}}}finally{null===t[io]&&jp(t,io),Ln(-1)}}(e,t);const a=e.components;null!==a&&ug(t,a,0);const l=e.viewQuery;if(null!==l&&yc(2,l,r),s){const c=e.viewCheckHooks;null!==c&&Ni(t,c)}else{const c=e.viewHooks;null!==c&&xi(t,c,2),ol(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[U]&=-73,Rf(t)}finally{rl()}}}function lg(e,t){for(let n=xh(e);null!==n;n=Oh(n))for(let r=Re;r<n.length;r++)cg(n[r],t)}function VI(e,t,n){cg(at(t,e),n)}function cg(e,t){if(!function ew(e){return 128==(128&e[U])}(e))return;const n=e[T],r=e[U];if(80&r&&0===t||1024&r||2===t)ag(n,e,n.template,e[be]);else if(e[eo]>0){lg(e,1);const o=n.components;null!==o&&ug(e,o,1)}}function ug(e,t,n){for(let r=0;r<t.length;r++)VI(e,t[r],n)}class To{get rootNodes(){const t=this._lView,n=t[T];return Ao(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[be]}set context(t){this._lView[be]=t}get destroyed(){return 256==(256&this._lView[U])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[ge];if(Ue(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(qi(t,r),ki(n,r))}this._attachedToViewContainer=!1}Ml(this._lView[T],this._lView)}onDestroy(t){!function Pf(e,t){if(256==(256&e[U]))throw new S(911,!1);null===e[mn]&&(e[mn]=[]),e[mn].push(t)}(this._lView,t)}markForCheck(){Mo(this._cdRefInjectingView||this._lView)}detach(){this._lView[U]&=-129}reattach(){this._lView[U]|=128}detectChanges(){_s(this._lView[T],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new S(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function Sb(e,t){Co(e,t,t[B],2,null,null)}(this._lView[T],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new S(902,!1);this._appRef=t}}class HI extends To{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;_s(t[T],t,t[be],!1)}checkNoChanges(){}get context(){return null}}class dg extends us{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=X(t);return new No(n,this.ngModule)}}function fg(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class jI{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=yi(r);const o=this.injector.get(t,nc,r);return o!==nc||n===nc?o:this.parentInjector.get(t,n,r)}}class No extends vp{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=fg(t.inputs);if(null!==n)for(const o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return fg(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function MD(e){return e.map(ID).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=(o=o||this.ngModule)instanceof en?o:o?.injector;i&&null!==this.componentDef.getStandaloneInjector&&(i=this.componentDef.getStandaloneInjector(i)||i);const s=i?new jI(t,i):t,a=s.get(Cp,null);if(null===a)throw new S(407,!1);const d={rendererFactory:a,sanitizer:s.get(LE,null),effectManager:s.get(ig,null),afterRenderEventManager:s.get(uc,null)},h=a.createRenderer(null,this.componentDef),p=this.componentDef.selectors[0][0]||"div",m=r?function uI(e,t,n,r){const i=r.get(kp,!1)||n===yt.ShadowDom,s=e.selectRootElement(t,i);return function dI(e){Wp(e)}(s),s}(h,r,this.componentDef.encapsulation,s):Gi(h,p,function BI(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(p)),A=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let D=null;null!==m&&(D=Xl(m,s,!0));const O=gc(0,null,null,1,0,null,null,null,null,null,null),L=ps(null,O,null,A,null,null,d,h,s,null,D);let Y,ot;nl(L);try{const cn=this.componentDef;let Wr,dd=null;cn.findHostDirectiveDefs?(Wr=[],dd=new Map,cn.findHostDirectiveDefs(cn,Wr,dd),Wr.push(cn)):Wr=[cn];const hP=function UI(e,t){const n=e[T],r=W;return e[r]=t,Ar(n,r,2,"#host",null)}(L,m),pP=function zI(e,t,n,r,o,i,s){const a=o[T];!function GI(e,t,n,r){for(const o of e)t.mergedAttrs=Jr(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(ms(t,t.mergedAttrs,!0),null!==n&&Wh(r,n,t))}(r,e,t,s);let l=null;null!==t&&(l=Xl(t,o[gn]));const c=i.rendererFactory.createRenderer(t,n);let u=16;n.signals?u=4096:n.onPush&&(u=64);const d=ps(o,qp(n),null,u,o[e.index],e,i,c,null,null,l);return a.firstCreatePass&&_c(a,e,r.length-1),gs(o,d),o[e.index]=d}(hP,m,cn,Wr,L,d,h);ot=xf(O,W),m&&function WI(e,t,n,r){if(r)Ba(e,n,["ng-version",VE.full]);else{const{attrs:o,classes:i}=function SD(e){const t=[],n=[];let r=1,o=2;for(;r<e.length;){let i=e[r];if("string"==typeof i)2===o?""!==i&&t.push(i,e[++r]):8===o&&n.push(i);else{if(!Ct(o))break;o=i}r++}return{attrs:t,classes:n}}(t.selectors[0]);o&&Ba(e,n,o),i&&i.length>0&&qh(e,n,i.join(" "))}}(h,cn,m,r),void 0!==n&&function ZI(e,t,n){const r=e.projection=[];for(let o=0;o<t.length;o++){const i=n[o];r.push(null!=i?Array.from(i):null)}}(ot,this.ngContentSelectors,n),Y=function qI(e,t,n,r,o,i){const s=Pe(),a=o[T],l=et(s,o);Yp(a,o,s,n,null,r);for(let u=0;u<n.length;u++)Ve(Vn(o,a,s.directiveStart+u,s),o);Kp(a,o,s),l&&Ve(l,o);const c=Vn(o,a,s.directiveStart+s.componentOffset,s);if(e[be]=o[be]=c,null!==i)for(const u of i)u(c,t);return fc(a,s,e),c}(pP,cn,Wr,dd,L,[QI]),Dc(O,L,null)}finally{rl()}return new $I(this.componentType,Y,Ir(ot,L),L,ot)}}class $I extends xE{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new HI(o),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const i=this._rootLView;Cc(i[T],i,o,t,n),this.previousInputValues.set(t,n),Mo(at(this._tNode.index,i))}}get injector(){return new qe(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function QI(){const e=Pe();Ti(E()[T],e)}function ae(e){let t=function hg(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let o;if(wt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new S(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);const s=e;s.inputs=vs(e.inputs),s.inputTransforms=vs(e.inputTransforms),s.declaredInputs=vs(e.declaredInputs),s.outputs=vs(e.outputs);const a=o.hostBindings;a&&XI(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&KI(e,l),c&&JI(e,c),hi(e.inputs,o.inputs),hi(e.declaredInputs,o.declaredInputs),hi(e.outputs,o.outputs),null!==o.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),hi(s.inputTransforms,o.inputTransforms)),wt(o)&&o.data.animation){const u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}const i=o.features;if(i)for(let s=0;s<i.length;s++){const a=i[s];a&&a.ngInherit&&a(e),a===ae&&(n=!1)}}t=Object.getPrototypeOf(t)}!function YI(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Jr(o.hostAttrs,n=Jr(n,o.hostAttrs))}}(r)}function vs(e){return e===xt?{}:e===te?[]:e}function KI(e,t){const n=e.viewQuery;e.viewQuery=n?(r,o)=>{t(r,o),n(r,o)}:t}function JI(e,t){const n=e.contentQueries;e.contentQueries=n?(r,o,i)=>{t(r,o,i),n(r,o,i)}:t}function XI(e,t){const n=e.hostBindings;e.hostBindings=n?(r,o)=>{t(r,o),n(r,o)}:t}function ys(e){return!!wc(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function wc(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function Vt(e,t,n){return e[t]=n}function He(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function $n(e,t,n,r){const o=He(e,t,n);return He(e,t+1,r)||o}function It(e,t,n,r){const o=E();return He(o,sr(),t)&&(ee(),Lt(ve(),o,e,t,n,r)),It}function Nr(e,t,n,r){return He(e,sr(),n)?t+H(n)+r:$}function C(e,t,n,r,o,i,s,a){const l=E(),c=ee(),u=e+W,d=c.firstCreatePass?function M0(e,t,n,r,o,i,s,a,l){const c=t.consts,u=Ar(t,e,4,s||null,vn(c,a));mc(t,n,u,vn(c,l)),Ti(t,u);const d=u.tView=gc(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return null!==t.queries&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}(u,c,l,t,n,r,o,i,s):c.data[u];Pt(d,!1);const h=Tg(c,l,d,e);Ai()&&Zi(c,l,h,d),Ve(h,l),gs(l,l[u]=Xp(h,l,h,d)),Ei(d)&&hc(c,l,d),null!=s&&pc(l,d,a)}let Tg=function Ng(e,t,n,r){return yn(!0),t[B].createComment("")};function g(e,t,n){const r=E();return He(r,sr(),t)&&ct(ee(),ve(),r,e,t,r[B],n,!1),g}function Ac(e,t,n,r,o){const s=o?"class":"style";Cc(e,n,t.inputs[s],s,r)}function y(e,t,n,r){const o=E(),i=ee(),s=W+e,a=o[B],l=i.firstCreatePass?function x0(e,t,n,r,o,i){const s=t.consts,l=Ar(t,e,2,r,vn(s,o));return mc(t,n,l,vn(s,i)),null!==l.attrs&&ms(l,l.attrs,!1),null!==l.mergedAttrs&&ms(l,l.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,l),l}(s,i,o,t,n,r):i.data[s],c=xg(i,o,l,a,t,e);o[s]=c;const u=Ei(l);return Pt(l,!0),Wh(a,c,l),32!=(32&l.flags)&&Ai()&&Zi(i,o,c,l),0===function ow(){return k.lFrame.elementDepthCount}()&&Ve(c,o),function iw(){k.lFrame.elementDepthCount++}(),u&&(hc(i,o,l),fc(i,l,o)),null!==r&&pc(o,l),y}function v(){let e=Pe();Ka()?Ja():(e=e.parent,Pt(e,!1));const t=e;(function aw(e){return k.skipHydrationRootTNode===e})(t)&&function dw(){k.skipHydrationRootTNode=null}(),function sw(){k.lFrame.elementDepthCount--}();const n=ee();return n.firstCreatePass&&(Ti(n,e),$a(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function Iw(e){return 0!=(8&e.flags)}(t)&&Ac(n,t,E(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function Mw(e){return 0!=(16&e.flags)}(t)&&Ac(n,t,E(),t.stylesWithoutHost,!1),v}function N(e,t,n,r){return y(e,t,n,r),v(),N}let xg=(e,t,n,r,o,i)=>(yn(!0),Gi(r,o,function Qf(){return k.lFrame.currentNamespace}()));function Z(e,t,n){const r=E(),o=ee(),i=e+W,s=o.firstCreatePass?function F0(e,t,n,r,o){const i=t.consts,s=vn(i,r),a=Ar(t,e,8,"ng-container",s);return null!==s&&ms(a,s,!0),mc(t,n,a,vn(i,o)),null!==t.queries&&t.queries.elementStart(t,a),a}(i,o,r,t,n):o.data[i];Pt(s,!0);const a=Rg(o,r,s,e);return r[i]=a,Ai()&&Zi(o,r,a,s),Ve(a,r),Ei(s)&&(hc(o,r,s),fc(o,s,r)),null!=n&&pc(r,s),Z}function Q(){let e=Pe();const t=ee();return Ka()?Ja():(e=e.parent,Pt(e,!1)),t.firstCreatePass&&(Ti(t,e),$a(e)&&t.queries.elementEnd(e)),Q}let Rg=(e,t,n,r)=>(yn(!0),Il(t[B],""));function De(){return E()}function Es(e){return!!e&&"function"==typeof e.then}function Fg(e){return!!e&&"function"==typeof e.subscribe}function F(e,t,n,r){const o=E(),i=ee(),s=Pe();return function kg(e,t,n,r,o,i,s){const a=Ei(r),c=e.firstCreatePass&&function ng(e){return e.cleanup||(e.cleanup=[])}(e),u=t[be],d=function tg(e){return e[Jn]||(e[Jn]=[])}(t);let h=!0;if(3&r.type||s){const w=et(r,t),I=s?s(w):w,A=d.length,D=s?L=>s(fe(L[r.index])):r.index;let O=null;if(!s&&a&&(O=function L0(e,t,n,r){const o=e.cleanup;if(null!=o)for(let i=0;i<o.length-1;i+=2){const s=o[i];if(s===n&&o[i+1]===r){const a=t[Jn],l=o[i+2];return a.length>l?a[l]:null}"string"==typeof s&&(i+=2)}return null}(e,t,o,r.index)),null!==O)(O.__ngLastListenerFn__||O).__ngNextListenerFn__=i,O.__ngLastListenerFn__=i,h=!1;else{i=Vg(r,t,u,i,!1);const L=n.listen(I,o,i);d.push(i,L),c&&c.push(o,D,A,A+1)}}else i=Vg(r,t,u,i,!1);const p=r.outputs;let m;if(h&&null!==p&&(m=p[o])){const w=m.length;if(w)for(let I=0;I<w;I+=2){const Y=t[m[I]][m[I+1]].subscribe(i),ot=d.length;d.push(i,Y),c&&c.push(o,r.index,ot,-(ot+1))}}}(i,o,o[B],s,e,t,r),F}function Lg(e,t,n,r){try{return Ft(6,t,n),!1!==n(r)}catch(o){return og(e,o),!1}finally{Ft(7,t,n)}}function Vg(e,t,n,r,o){return function i(s){if(s===Function)return r;Mo(e.componentOffset>-1?at(e.index,t):t);let l=Lg(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)l=Lg(t,n,c,s)&&l,c=c.__ngNextListenerFn__;return o&&!1===l&&s.preventDefault(),l}}function _(e=1){return function _w(e){return(k.lFrame.contextLView=function vw(e,t){for(;e>0;)t=t[er],e--;return t}(e,k.lFrame.contextLView))[be]}(e)}function bn(e,t,n){return Tc(e,"",t,"",n),bn}function Tc(e,t,n,r,o){const i=E(),s=Nr(i,t,n,r);return s!==$&&ct(ee(),ve(),i,e,s,i[B],o,!1),Tc}function Is(e,t){return e<<17|t<<2}function En(e){return e>>17&32767}function Nc(e){return 2|e}function Un(e){return(131068&e)>>2}function xc(e,t){return-131069&e|t<<2}function Oc(e){return 1|e}function Wg(e,t,n,r,o){const i=e[n+1],s=null===t;let a=r?En(i):Un(i),l=!1;for(;0!==a&&(!1===l||s);){const u=e[a+1];q0(e[a],t)&&(l=!0,e[a+1]=r?Oc(u):Nc(u)),a=r?En(u):Un(u)}l&&(e[n+1]=r?Nc(i):Oc(i))}function q0(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&pr(e,t)>=0}const Ne={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Zg(e){return e.substring(Ne.key,Ne.keyEnd)}function Qg(e,t){const n=Ne.textEnd;return n===t?-1:(t=Ne.keyEnd=function Y0(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,Ne.key=t,n),Vr(e,t,n))}function Vr(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Ms(e,t){return function Mt(e,t,n,r){const o=E(),i=ee(),s=Jt(2);i.firstUpdatePass&&nm(i,e,s,r),t!==$&&He(o,s,t)&&om(i,i.data[Ge()],o,o[B],e,o[s+1]=function l1(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Ae(Dn(e)))),e}(t,n),r,s)}(e,t,null,!0),Ms}function Bt(e,t){for(let n=function Z0(e){return function Kg(e){Ne.key=0,Ne.keyEnd=0,Ne.value=0,Ne.valueEnd=0,Ne.textEnd=e.length}(e),Qg(e,Vr(e,0,Ne.textEnd))}(t);n>=0;n=Qg(t,n))lt(e,Zg(t),!0)}function tm(e,t){return t>=e.expandoStartIndex}function nm(e,t,n,r){const o=e.data;if(null===o[n+1]){const i=o[Ge()],s=tm(e,n);sm(i,r)&&null===t&&!s&&(t=!1),t=function t1(e,t,n,r){const o=function el(e){const t=k.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let i=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=Po(n=Rc(null,e,t,n,r),t.attrs,r),i=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==o)if(n=Rc(o,e,t,n,r),null===i){let l=function n1(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==Un(r))return e[En(r)]}(e,t,r);void 0!==l&&Array.isArray(l)&&(l=Rc(null,e,t,l[1],r),l=Po(l,t.attrs,r),function r1(e,t,n,r){e[En(n?t.classBindings:t.styleBindings)]=r}(e,t,r,l))}else i=function o1(e,t,n){let r;const o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++)r=Po(r,e[i].hostAttrs,n);return Po(r,t.attrs,n)}(e,t,r)}return void 0!==i&&(r?t.residualClasses=i:t.residualStyles=i),n}(o,i,t,r),function z0(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=En(s),l=Un(s);e[r]=n;let u,c=!1;if(Array.isArray(n)?(u=n[1],(null===u||pr(n,u)>0)&&(c=!0)):u=n,o)if(0!==l){const h=En(e[a+1]);e[r+1]=Is(h,a),0!==h&&(e[h+1]=xc(e[h+1],r)),e[a+1]=function $0(e,t){return 131071&e|t<<17}(e[a+1],r)}else e[r+1]=Is(a,0),0!==a&&(e[a+1]=xc(e[a+1],r)),a=r;else e[r+1]=Is(l,0),0===a?a=r:e[l+1]=xc(e[l+1],r),l=r;c&&(e[r+1]=Nc(e[r+1])),Wg(e,u,r,!0),Wg(e,u,r,!1),function G0(e,t,n,r,o){const i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof t&&pr(i,t)>=0&&(n[r+1]=Oc(n[r+1]))}(t,u,e,r,i),s=Is(a,l),i?t.classBindings=s:t.styleBindings=s}(o,i,t,n,s,r)}}function Rc(e,t,n,r,o){let i=null;const s=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<s&&(i=t[a],r=Po(r,i.hostAttrs,o),i!==e);)a++;return null!==e&&(n.directiveStylingLast=a),r}function Po(e,t,n){const r=n?1:2;let o=-1;if(null!==t)for(let i=0;i<t.length;i++){const s=t[i];"number"==typeof s?o=s:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),lt(e,s,!!n||t[++i]))}return void 0===e?null:e}function om(e,t,n,r,o,i,s,a){if(!(3&t.type))return;const l=e.data,c=l[a+1],u=function U0(e){return 1==(1&e)}(c)?im(l,t,n,o,Un(c),s):void 0;Ss(u)||(Ss(i)||function j0(e){return 2==(2&e)}(c)&&(i=im(l,null,n,o,a,s)),function Lb(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=-1===r.indexOf("-")?void 0:Cn.DashCase;null==o?e.removeStyle(n,r,i):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Cn.Important),e.setStyle(n,r,o,i))}}(r,s,Si(Ge(),n),o,i))}function im(e,t,n,r,o,i){const s=null===t;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),u=c?l[1]:l,d=null===u;let h=n[o+1];h===$&&(h=d?te:void 0);let p=d?hl(h,r):u===r?h:void 0;if(c&&!Ss(p)&&(p=hl(l,r)),Ss(p)&&(a=p,s))return a;const m=e[o+1];o=s?En(m):Un(m)}if(null!==t){let l=i?t.residualClasses:t.residualStyles;null!=l&&(a=hl(l,r))}return a}function Ss(e){return void 0!==e}function sm(e,t){return 0!=(e.flags&(t?8:16))}function b(e,t=""){const n=E(),r=ee(),o=e+W,i=r.firstCreatePass?Ar(r,o,1,t,null):r.data[o],s=am(r,n,i,t,e);n[o]=s,Ai()&&Zi(r,n,s,i),Pt(i,!1)}let am=(e,t,n,r,o)=>(yn(!0),function zi(e,t){return e.createText(t)}(t[B],r));function x(e){return z("",e,""),x}function z(e,t,n){const r=E(),o=Nr(r,e,t,n);return o!==$&&function rn(e,t,n){const r=Si(t,e);!function Fh(e,t,n){e.setValue(t,n)}(e[B],r,n)}(r,Ge(),o),z}function jt(e,t,n){!function St(e,t,n,r){const o=ee(),i=Jt(2);o.firstUpdatePass&&nm(o,null,i,r);const s=E();if(n!==$&&He(s,i,n)){const a=o.data[Ge()];if(sm(a,r)&&!tm(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;null!==l&&(n=Ma(l,n||"")),Ac(o,a,s,n,r)}else!function a1(e,t,n,r,o,i,s,a){o===$&&(o=te);let l=0,c=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;null!==u||null!==d;){const h=l<o.length?o[l+1]:void 0,p=c<i.length?i[c+1]:void 0;let w,m=null;u===d?(l+=2,c+=2,h!==p&&(m=d,w=p)):null===d||null!==u&&u<d?(l+=2,m=u):(c+=2,m=d,w=p),null!==m&&om(e,t,n,r,m,w,s,a),u=l<o.length?o[l]:null,d=c<i.length?i[c]:null}}(o,a,s,s[B],s[i+1],s[i+1]=function i1(e,t,n){if(null==n||""===n)return te;const r=[],o=Dn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if("object"==typeof o)for(const i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else"string"==typeof o&&t(r,o);return r}(e,t,n),r,i)}}(lt,Bt,Nr(E(),e,t,n),!0)}const Br="en-US";let Tm=Br;function kc(e,t,n,r,o){if(e=V(e),Array.isArray(e))for(let i=0;i<e.length;i++)kc(e[i],t,n,r,o);else{const i=ee(),s=E(),a=Pe();let l=Bn(e)?e:V(e.provide);const c=fp(e),u=1048575&a.providerIndexes,d=a.directiveStart,h=a.providerIndexes>>20;if(Bn(e)||!e.multi){const p=new co(c,o,M),m=Vc(l,t,o?u:u+h,d);-1===m?(cl(Ri(a,s),i,l),Lc(i,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(p),s.push(p)):(n[m]=p,s[m]=p)}else{const p=Vc(l,t,u+h,d),m=Vc(l,t,u,u+h),I=m>=0&&n[m];if(o&&!I||!o&&!(p>=0&&n[p])){cl(Ri(a,s),i,l);const A=function AM(e,t,n,r,o){const i=new co(e,n,M);return i.multi=[],i.index=t,i.componentProviders=0,e_(i,o,r&&!n),i}(o?SM:MM,n.length,o,r,c);!o&&I&&(n[m].providerFactory=A),Lc(i,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(A),s.push(A)}else Lc(i,e,p>-1?p:m,e_(n[o?m:p],c,!o&&r));!o&&r&&I&&n[m].componentProviders++}}}function Lc(e,t,n,r){const o=Bn(t),i=function pE(e){return!!e.useClass}(t);if(o||i){const l=(i?V(t.useClass):t).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const u=c.indexOf(n);-1===u?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function e_(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Vc(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function MM(e,t,n,r){return Hc(this.multi,[])}function SM(e,t,n,r){const o=this.multi;let i;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=Vn(n,n[T],this.providerFactory.index,r);i=a.slice(0,s),Hc(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Hc(o,i);return i}function Hc(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function me(e,t=[]){return n=>{n.providersResolver=(r,o)=>function IM(e,t,n){const r=ee();if(r.firstCreatePass){const o=wt(e);kc(n,r.data,r.blueprint,o,!0),kc(t,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,t)}}class Gn{}class TM{}class Bc extends Gn{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new dg(this);const o=function st(e,t){const n=e[Wd]||null;if(!n&&!0===t)throw new Error(`Type ${Ae(e)} does not have '\u0275mod' property.`);return n}(t);this._bootstrapComponents=function nn(e){return e instanceof Function?e():e}(o.bootstrap),this._r3Injector=Ip(t,n,[{provide:Gn,useValue:this},{provide:us,useValue:this.componentFactoryResolver},...r],Ae(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class jc extends TM{constructor(t){super(),this.moduleType=t}create(t){return new Bc(this.moduleType,t,[])}}function Os(e,t,n,r){return function l_(e,t,n,r,o,i){const s=t+n;return He(e,s,o)?Vt(e,s+1,i?r.call(i,o):r(o)):jo(e,s+1)}(E(),ze(),e,t,n,r)}function Uc(e,t,n,r,o){return function c_(e,t,n,r,o,i,s){const a=t+n;return $n(e,a,o,i)?Vt(e,a+2,s?r.call(s,o,i):r(o,i)):jo(e,a+2)}(E(),ze(),e,t,n,r,o)}function xe(e,t,n,r,o,i){return u_(E(),ze(),e,t,n,r,o,i)}function jo(e,t){const n=e[t];return n===$?void 0:n}function u_(e,t,n,r,o,i,s,a){const l=t+n;return function Cs(e,t,n,r,o){const i=$n(e,t,n,r);return He(e,t+2,o)||i}(e,l,o,i,s)?Vt(e,l+3,a?r.call(a,o,i,s):r(o,i,s)):jo(e,l+3)}function p_(e,t,n,r,o){const i=e+W,s=E(),a=function or(e,t){return e[t]}(s,i);return function $o(e,t){return e[T].data[t].pure}(s,i)?u_(s,ze(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function rS(e,t,n,r=!0){const o=t[T];if(function Tb(e,t,n,r){const o=Re+r,i=n.length;r>0&&(n[o-1][Dt]=t),r<i-Re?(t[Dt]=n[o],uh(n,Re+r,t)):(n.push(t),t[Dt]=null),t[ge]=n;const s=t[no];null!==s&&n!==s&&function Nb(e,t){const n=e[nr];t[Ee]!==t[ge][ge][Ee]&&(e[cf]=!0),null===n?e[nr]=[t]:n.push(t)}(s,t);const a=t[Ot];null!==a&&a.insertView(e),t[U]|=128}(o,t,e,n),r){const i=Nl(n,e),s=t[B],a=Wi(s,e[Rt]);null!==a&&function Mb(e,t,n,r,o,i){r[ye]=o,r[ke]=t,Co(e,r,n,1,o,i)}(o,e[ke],s,t,a,i)}}let on=(()=>{class t{}return t.__NG_ELEMENT_ID__=sS,t})();const oS=on,iS=class extends oS{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const o=function nS(e,t,n,r){const o=t.tView,a=ps(e,o,n,4096&e[U]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);a[no]=e[t.index];const c=e[Ot];return null!==c&&(a[Ot]=c.createEmbeddedView(o)),Dc(o,a,n),a}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new To(o)}};function sS(){return function Rs(e,t){return 4&e.type?new iS(t,e,Ir(e,t)):null}(Pe(),E())}let $t=(()=>{class t{}return t.__NG_ELEMENT_ID__=fS,t})();function fS(){return function D_(e,t){let n;const r=t[e.index];return Ue(r)?n=r:(n=Xp(r,t,null,e),t[e.index]=n,gs(t,n)),w_(n,t,e,r),new y_(n,e,t)}(Pe(),E())}const hS=$t,y_=class extends hS{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Ir(this._hostTNode,this._hostLView)}get injector(){return new qe(this._hostTNode,this._hostLView)}get parentInjector(){const t=Fi(this._hostTNode,this._hostLView);if(sl(t)){const n=fo(t,this._hostLView),r=uo(t);return new qe(n[T].data[r+8],n)}return new qe(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=C_(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Re}createEmbeddedView(t,n,r){let o,i;"number"==typeof r?o=r:null!=r&&(o=r.index,i=r.injector);const a=t.createEmbeddedViewImpl(n||{},i,null);return this.insertImpl(a,o,false),a}createComponent(t,n,r,o,i){const s=t&&!function po(e){return"function"==typeof e}(t);let a;if(s)a=n;else{const w=n||{};a=w.index,r=w.injector,o=w.projectableNodes,i=w.environmentInjector||w.ngModuleRef}const l=s?t:new No(X(t)),c=r||this.parentInjector;if(!i&&null==l.ngModule){const I=(s?c:this.parentInjector).get(en,null);I&&(i=I)}X(l.componentType??{});const p=l.create(c,o,null,i);return this.insertImpl(p.hostView,a,false),p}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const o=t._lView;if(function tw(e){return Ue(e[ge])}(o)){const l=this.indexOf(t);if(-1!==l)this.detach(l);else{const c=o[ge],u=new y_(c,c[ke],c[ge]);u.detach(u.indexOf(t))}}const s=this._adjustIndex(n),a=this._lContainer;return rS(a,o,s,!r),t.attachToViewContainerRef(),uh(Gc(a),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=C_(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=qi(this._lContainer,n);r&&(ki(Gc(this._lContainer),n),Ml(r[T],r))}detach(t){const n=this._adjustIndex(t,-1),r=qi(this._lContainer,n);return r&&null!=ki(Gc(this._lContainer),n)?new To(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function C_(e){return e[8]}function Gc(e){return e[8]||(e[8]=[])}let w_=function b_(e,t,n,r){if(e[Rt])return;let o;o=8&n.type?fe(r):function pS(e,t){const n=e[B],r=n.createComment(""),o=et(t,e);return Hn(n,Wi(n,o),r,function Fb(e,t){return e.nextSibling(t)}(n,o),!1),r}(t,n),e[Rt]=o};const ZS=new R("Application Initializer");let tu=(()=>{var e;class t{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((r,o)=>{this.resolve=r,this.reject=o}),this.appInits=le(ZS,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const r=[];for(const i of this.appInits){const s=i();if(Es(s))r.push(s);else if(Fg(s)){const a=new Promise((l,c)=>{s.subscribe({complete:l,error:c})});r.push(a)}}const o=()=>{this.done=!0,this.resolve()};Promise.all(r).then(()=>{o()}).catch(i=>{this.reject(i)}),0===r.length&&o(),this.initialized=!0}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"root"}),t})();const sn=new R("LocaleId",{providedIn:"root",factory:()=>le(sn,K.Optional|K.SkipSelf)||function YS(){return typeof $localize<"u"&&$localize.locale||Br}()});let XS=(()=>{var e;class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new ZC(!1)}add(){this.hasPendingTasks.next(!0);const r=this.taskId++;return this.pendingTasks.add(r),r}remove(r){this.pendingTasks.delete(r),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"root"}),t})();const Q_=new R(""),Ls=new R("");let su,ou=(()=>{var e;class t{constructor(r,o,i){this._ngZone=r,this.registry=o,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,su||(function CA(e){su=e}(i),i.addToWindow(o)),this._watchAngularEvents(),r.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Ce.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let r=this._callbacks.pop();clearTimeout(r.timeoutId),r.doneCb(this._didWork)}this._didWork=!1});else{let r=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>!o.updateCb||!o.updateCb(r)||(clearTimeout(o.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(r=>({source:r.source,creationLocation:r.creationLocation,data:r.data})):[]}addCallback(r,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),r(this._didWork,this.getPendingTasks())},o)),this._callbacks.push({doneCb:r,timeoutId:s,updateCb:i})}whenStable(r,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(r,o,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(r){this.registry.registerApplication(r,this)}unregisterApplication(r){this.registry.unregisterApplication(r)}findProviders(r,o,i){return[]}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Ce),J(iu),J(Ls))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})(),iu=(()=>{var e;class t{constructor(){this._applications=new Map}registerApplication(r,o){this._applications.set(r,o)}unregisterApplication(r){this._applications.delete(r)}unregisterAllApplications(){this._applications.clear()}getTestability(r){return this._applications.get(r)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(r,o=!0){return su?.findTestabilityInTree(this,r,o)??null}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"platform"}),t})(),In=null;const Y_=new R("AllowMultipleToken"),au=new R("PlatformDestroyListeners"),K_=new R("appBootstrapListener");function ev(e,t,n=[]){const r=`Platform: ${t}`,o=new R(r);return(i=[])=>{let s=lu();if(!s||s.injector.get(Y_,!1)){const a=[...n,...i,{provide:o,useValue:!0}];e?e(a):function bA(e){if(In&&!In.get(Y_,!1))throw new S(400,!1);(function J_(){!function BD(e){Df=e}(()=>{throw new S(600,!1)})})(),In=e;const t=e.get(nv);(function X_(e){e.get(hp,null)?.forEach(n=>n())})(e)}(function tv(e=[],t){return Et.create({name:t,providers:[{provide:$l,useValue:"platform"},{provide:au,useValue:new Set([()=>In=null])},...e]})}(a,r))}return function IA(e){const t=lu();if(!t)throw new S(401,!1);return t}()}}function lu(){return In?.get(nv)??null}let nv=(()=>{var e;class t{constructor(r){this._injector=r,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(r,o){const i=function MA(e="zone.js",t){return"noop"===e?new YE:"zone.js"===e?new Ce(t):e}(o?.ngZone,function rv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:o?.ngZoneEventCoalescing,runCoalescing:o?.ngZoneRunCoalescing}));return i.run(()=>{const s=function xM(e,t,n){return new Bc(e,t,n)}(r.moduleType,this.injector,function lv(e){return[{provide:Ce,useFactory:e},{provide:Xi,multi:!0,useFactory:()=>{const t=le(AA,{optional:!0});return()=>t.initialize()}},{provide:av,useFactory:SA},{provide:Np,useFactory:xp}]}(()=>i)),a=s.injector.get(tn,null);return i.runOutsideAngular(()=>{const l=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{Vs(this._modules,s),l.unsubscribe()})}),function ov(e,t,n){try{const r=n();return Es(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(a,i,()=>{const l=s.injector.get(tu);return l.runInitializers(),l.donePromise.then(()=>(function Nm(e){ft(e,"Expected localeId to be defined"),"string"==typeof e&&(Tm=e.toLowerCase().replace(/_/g,"-"))}(s.injector.get(sn,Br)||Br),this._moduleDoBootstrap(s),s))})})}bootstrapModule(r,o=[]){const i=iv({},o);return function DA(e,t,n){const r=new jc(n);return Promise.resolve(r)}(0,0,r).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(r){const o=r.injector.get(Go);if(r._bootstrapComponents.length>0)r._bootstrapComponents.forEach(i=>o.bootstrap(i));else{if(!r.instance.ngDoBootstrap)throw new S(-403,!1);r.instance.ngDoBootstrap(o)}this._modules.push(r)}onDestroy(r){this._destroyListeners.push(r)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new S(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());const r=this._injector.get(au,null);r&&(r.forEach(o=>o()),r.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Et))},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"platform"}),t})();function iv(e,t){return Array.isArray(t)?t.reduce(iv,e):{...e,...t}}let Go=(()=>{var e;class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=le(av),this.zoneIsStable=le(Np),this.componentTypes=[],this.components=[],this.isStable=le(XS).hasPendingTasks.pipe(function KC(e,t){return un((n,r)=>{let o=null,i=0,s=!1;const a=()=>s&&!o&&r.complete();n.subscribe(new zt(r,l=>{o?.unsubscribe();let c=0;const u=i++;dn(e(l,u)).subscribe(o=new zt(r,d=>r.next(t?t(l,d,u,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}(r=>r?function QC(...e){return fi(e,Fd(e))}(!1):this.zoneIsStable),function JC(e,t=Ca){return e=e??XC,un((n,r)=>{let o,i=!0;n.subscribe(new zt(r,s=>{const a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}(),Vd()),this._injector=le(en)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(r,o){const i=r instanceof vp;if(!this._injector.get(tu).done)throw!i&&function Xr(e){const t=X(e)||Oe(e)||$e(e);return null!==t&&t.standalone}(r),new S(405,!1);let a;a=i?r:this._injector.get(us).resolveComponentFactory(r),this.componentTypes.push(a.componentType);const l=function wA(e){return e.isBoundToModule}(a)?void 0:this._injector.get(Gn),u=a.create(Et.NULL,[],o||a.selector,l),d=u.location.nativeElement,h=u.injector.get(Q_,null);return h?.registerApplication(d),u.onDestroy(()=>{this.detachView(u.hostView),Vs(this.components,u),h?.unregisterApplication(d)}),this._loadComponent(u),u}tick(){if(this._runningTick)throw new S(101,!1);try{this._runningTick=!0;for(let r of this._views)r.detectChanges()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1}}attachView(r){const o=r;this._views.push(o),o.attachToAppRef(this)}detachView(r){const o=r;Vs(this._views,o),o.detachFromAppRef()}_loadComponent(r){this.attachView(r.hostView),this.tick(),this.components.push(r);const o=this._injector.get(K_,[]);o.push(...this._bootstrapListeners),o.forEach(i=>i(r))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(r=>r()),this._views.slice().forEach(r=>r.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(r){return this._destroyListeners.push(r),()=>Vs(this._destroyListeners,r)}destroy(){if(this._destroyed)throw new S(406,!1);const r=this._injector;r.destroy&&!r.destroyed&&r.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"root"}),t})();function Vs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const av=new R("",{providedIn:"root",factory:()=>le(tn).handleError.bind(void 0)});function SA(){const e=le(Ce),t=le(tn);return n=>e.runOutsideAngular(()=>t.handleError(n))}let AA=(()=>{var e;class t{constructor(){this.zone=le(Ce),this.applicationRef=le(Go)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac,providedIn:"root"}),t})();let uv=(()=>{class t{}return t.__NG_ELEMENT_ID__=NA,t})();function NA(e){return function xA(e,t,n){if(Pn(e)&&!n){const r=at(e.index,t);return new To(r,r)}return 47&e.type?new To(t[Ee],t):null}(Pe(),E(),16==(16&e))}class hv{constructor(){}supports(t){return ys(t)}create(t){return new LA(t)}}const kA=(e,t)=>t;class LA{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||kA}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){const s=!r||n&&n.currentIndex<gv(r,o,i)?n:r,a=gv(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)o++;else{i||(i=[]);const c=a-o,u=l-o;if(c!=u){for(let h=0;h<c;h++){const p=h<i.length?i[h]:i[h]=0,m=p+h;u<=m&&m<c&&(i[h]=p+1)}i[s.previousIndex]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!ys(t))throw new S(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let o,i,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)):(n=this._mismatch(n,i,s,a),r=!0),n=n._next}else o=0,function l0(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,a=>{s=this._trackByFn(o,a),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)):(n=this._mismatch(n,a,s,o),r=!0),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return null===t?i=this._itTail:(i=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new VA(n,r),i,o),t}_verifyReinsertion(t,n,r,o){let i=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==i?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const o=t._prevRemoved,i=t._nextRemoved;return null===o?this._removalsHead=i:o._nextRemoved=i,null===i?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const o=null===n?this._itHead:n._next;return t._next=o,t._prev=n,null===o?this._itTail=t:o._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new pv),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new pv),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class VA{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class HA{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class pv{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new HA,this.map.set(n,r)),r.add(t)}get(t,n){const o=this.map.get(t);return o?o.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function gv(e,t,n){const r=e.previousIndex;if(null===r)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}class mv{constructor(){}supports(t){return t instanceof Map||wc(t)}create(){return new BA}}class BA{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||wc(t)))throw new S(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const o=this._records.get(t);this._maybeAddToChanges(o,n);const i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}const r=new jA(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class jA{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function _v(){return new js([new hv])}let js=(()=>{var e;class t{constructor(r){this.factories=r}static create(r,o){if(null!=o){const i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||_v()),deps:[[t,new ml,new gl]]}}find(r){const o=this.factories.find(i=>i.supports(r));if(null!=o)return o;throw new S(901,!1)}}return(e=t).\u0275prov=se({token:e,providedIn:"root",factory:_v}),t})();function vv(){return new qo([new mv])}let qo=(()=>{var e;class t{constructor(r){this.factories=r}static create(r,o){if(o){const i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||vv()),deps:[[t,new ml,new gl]]}}find(r){const o=this.factories.find(i=>i.supports(r));if(o)return o;throw new S(901,!1)}}return(e=t).\u0275prov=se({token:e,providedIn:"root",factory:vv}),t})();const zA=ev(null,"core",[]);let GA=(()=>{var e;class t{constructor(r){}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Go))},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({}),t})();let gu=null;function Wo(){return gu}class oT{}const Wn=new R("DocumentToken"),Iu=/\s+/,Pv=[];let Yo=(()=>{var e;class t{constructor(r,o,i,s){this._iterableDiffers=r,this._keyValueDiffers=o,this._ngEl=i,this._renderer=s,this.initialClasses=Pv,this.stateMap=new Map}set klass(r){this.initialClasses=null!=r?r.trim().split(Iu):Pv}set ngClass(r){this.rawClass="string"==typeof r?r.trim().split(Iu):r}ngDoCheck(){for(const o of this.initialClasses)this._updateState(o,!0);const r=this.rawClass;if(Array.isArray(r)||r instanceof Set)for(const o of r)this._updateState(o,!0);else if(null!=r)for(const o of Object.keys(r))this._updateState(o,!!r[o]);this._applyStateDiff()}_updateState(r,o){const i=this.stateMap.get(r);void 0!==i?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(r,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(const r of this.stateMap){const o=r[0],i=r[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(r,o){(r=r.trim()).length>0&&r.split(Iu).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}}return(e=t).\u0275fac=function(r){return new(r||e)(M(js),M(qo),M(bt),M(jn))},e.\u0275dir=j({type:e,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0}),t})();class qT{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let zr=(()=>{var e;class t{set ngForOf(r){this._ngForOf=r,this._ngForOfDirty=!0}set ngForTrackBy(r){this._trackByFn=r}get ngForTrackBy(){return this._trackByFn}constructor(r,o,i){this._viewContainer=r,this._template=o,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(r){r&&(this._template=r)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const r=this._ngForOf;!this._differ&&r&&(this._differ=this._differs.find(r).create(this.ngForTrackBy))}if(this._differ){const r=this._differ.diff(this._ngForOf);r&&this._applyChanges(r)}}_applyChanges(r){const o=this._viewContainer;r.forEachOperation((i,s,a)=>{if(null==i.previousIndex)o.createEmbeddedView(this._template,new qT(i.item,this._ngForOf,-1,-1),null===a?void 0:a);else if(null==a)o.remove(null===s?void 0:s);else if(null!==s){const l=o.get(s);o.move(l,a),Lv(l,i)}});for(let i=0,s=o.length;i<s;i++){const l=o.get(i).context;l.index=i,l.count=s,l.ngForOf=this._ngForOf}r.forEachIdentityChange(i=>{Lv(o.get(i.currentIndex),i)})}static ngTemplateContextGuard(r,o){return!0}}return(e=t).\u0275fac=function(r){return new(r||e)(M($t),M(on),M(js))},e.\u0275dir=j({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0}),t})();function Lv(e,t){e.context.$implicit=t.item}let Zn=(()=>{var e;class t{constructor(r,o){this._viewContainer=r,this._context=new WT,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=o}set ngIf(r){this._context.$implicit=this._context.ngIf=r,this._updateView()}set ngIfThen(r){Vv("ngIfThen",r),this._thenTemplateRef=r,this._thenViewRef=null,this._updateView()}set ngIfElse(r){Vv("ngIfElse",r),this._elseTemplateRef=r,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(r,o){return!0}}return(e=t).\u0275fac=function(r){return new(r||e)(M($t),M(on))},e.\u0275dir=j({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0}),t})();class WT{constructor(){this.$implicit=null,this.ngIf=null}}function Vv(e,t){if(t&&!t.createEmbeddedView)throw new Error(`${e} must be a TemplateRef, but received '${Ae(t)}'.`)}let jv=(()=>{var e;class t{transform(r,o,i){if(null==r)return null;if(!this.supports(r))throw function Tt(e,t){return new S(2100,!1)}();return r.slice(o,i)}supports(r){return"string"==typeof r||Array.isArray(r)}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275pipe=Je({name:"slice",type:e,pure:!1,standalone:!0}),t})(),vN=(()=>{var e;class t{}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({}),t})();function Uv(e){return"server"===e}class qN extends oT{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class Ou extends qN{static makeCurrent(){!function rT(e){gu||(gu=e)}(new Ou)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,n){return(n=n||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return"window"===n?window:"document"===n?t:"body"===n?t.body:null}getBaseHref(t){const n=function WN(){return Jo=Jo||document.querySelector("base"),Jo?Jo.getAttribute("href"):null}();return null==n?null:function ZN(e){ta=ta||document.createElement("a"),ta.setAttribute("href",e);const t=ta.pathname;return"/"===t.charAt(0)?t:`/${t}`}(n)}resetBaseElement(){Jo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return function zT(e,t){t=encodeURIComponent(t);for(const n of e.split(";")){const r=n.indexOf("="),[o,i]=-1==r?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}(document.cookie,t)}}let ta,Jo=null,YN=(()=>{var e;class t{build(){return new XMLHttpRequest}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();const Ru=new R("EventManagerPlugins");let Zv=(()=>{var e;class t{constructor(r,o){this._zone=o,this._eventNameToPlugin=new Map,r.forEach(i=>{i.manager=this}),this._plugins=r.slice().reverse()}addEventListener(r,o,i){return this._findPluginFor(o).addEventListener(r,o,i)}getZone(){return this._zone}_findPluginFor(r){let o=this._eventNameToPlugin.get(r);if(o)return o;if(o=this._plugins.find(s=>s.supports(r)),!o)throw new S(5101,!1);return this._eventNameToPlugin.set(r,o),o}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Ru),J(Ce))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();class Qv{constructor(t){this._doc=t}}const Fu="ng-app-id";let Yv=(()=>{var e;class t{constructor(r,o,i,s={}){this.doc=r,this.appId=o,this.nonce=i,this.platformId=s,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Uv(s),this.resetHostNodes()}addStyles(r){for(const o of r)1===this.changeUsageCount(o,1)&&this.onStyleAdded(o)}removeStyles(r){for(const o of r)this.changeUsageCount(o,-1)<=0&&this.onStyleRemoved(o)}ngOnDestroy(){const r=this.styleNodesInDOM;r&&(r.forEach(o=>o.remove()),r.clear());for(const o of this.getAllStyles())this.onStyleRemoved(o);this.resetHostNodes()}addHost(r){this.hostNodes.add(r);for(const o of this.getAllStyles())this.addStyleToHost(r,o)}removeHost(r){this.hostNodes.delete(r)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(r){for(const o of this.hostNodes)this.addStyleToHost(o,r)}onStyleRemoved(r){const o=this.styleRef;o.get(r)?.elements?.forEach(i=>i.remove()),o.delete(r)}collectServerRenderedStyles(){const r=this.doc.head?.querySelectorAll(`style[${Fu}="${this.appId}"]`);if(r?.length){const o=new Map;return r.forEach(i=>{null!=i.textContent&&o.set(i.textContent,i)}),o}return null}changeUsageCount(r,o){const i=this.styleRef;if(i.has(r)){const s=i.get(r);return s.usage+=o,s.usage}return i.set(r,{usage:o,elements:[]}),o}getStyleElement(r,o){const i=this.styleNodesInDOM,s=i?.get(o);if(s?.parentNode===r)return i.delete(o),s.removeAttribute(Fu),s;{const a=this.doc.createElement("style");return this.nonce&&a.setAttribute("nonce",this.nonce),a.textContent=o,this.platformIsServer&&a.setAttribute(Fu,this.appId),a}}addStyleToHost(r,o){const i=this.getStyleElement(r,o);r.appendChild(i);const s=this.styleRef,a=s.get(o)?.elements;a?a.push(i):s.set(o,{elements:[i],usage:1})}resetHostNodes(){const r=this.hostNodes;r.clear(),r.add(this.doc.head)}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Wn),J(os),J(pp,8),J(wr))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();const Pu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},ku=/%COMP%/g,ex=new R("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function Jv(e,t){return t.map(n=>n.replace(ku,e))}let Xv=(()=>{var e;class t{constructor(r,o,i,s,a,l,c,u=null){this.eventManager=r,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=a,this.platformId=l,this.ngZone=c,this.nonce=u,this.rendererByCompId=new Map,this.platformIsServer=Uv(l),this.defaultRenderer=new Lu(r,a,c,this.platformIsServer)}createRenderer(r,o){if(!r||!o)return this.defaultRenderer;this.platformIsServer&&o.encapsulation===yt.ShadowDom&&(o={...o,encapsulation:yt.Emulated});const i=this.getOrCreateRenderer(r,o);return i instanceof ty?i.applyToHost(r):i instanceof Vu&&i.applyStyles(),i}getOrCreateRenderer(r,o){const i=this.rendererByCompId;let s=i.get(o.id);if(!s){const a=this.doc,l=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,h=this.platformIsServer;switch(o.encapsulation){case yt.Emulated:s=new ty(c,u,o,this.appId,d,a,l,h);break;case yt.ShadowDom:return new ox(c,u,r,o,a,l,this.nonce,h);default:s=new Vu(c,u,o,d,a,l,h)}i.set(o.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Zv),J(Yv),J(os),J(ex),J(Wn),J(wr),J(Ce),J(pp))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();class Lu{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(Pu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(ey(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(ey(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){t&&t.removeChild(n)}selectRootElement(t,n){let r="string"==typeof t?this.doc.querySelector(t):t;if(!r)throw new S(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;const i=Pu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){const o=Pu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Cn.DashCase|Cn.Important)?t.style.setProperty(n,r,o&Cn.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Cn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t[n]=r}setValue(t,n){t.nodeValue=n}listen(t,n,r){if("string"==typeof t&&!(t=Wo().getGlobalEventTarget(this.doc,t)))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if("__ngUnwrap__"===n)return t;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))&&n.preventDefault()}}}function ey(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class ox extends Lu{constructor(t,n,r,o,i,s,a,l){super(t,i,s,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=Jv(o.id,o.styles);for(const u of c){const d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(this.nodeOrShadowRoot(t),n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class Vu extends Lu{constructor(t,n,r,o,i,s,a,l){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=l?Jv(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class ty extends Vu{constructor(t,n,r,o,i,s,a,l){const c=o+"-"+r.id;super(t,n,r,i,s,a,l,c),this.contentAttr=function tx(e){return"_ngcontent-%COMP%".replace(ku,e)}(c),this.hostAttr=function nx(e){return"_nghost-%COMP%".replace(ku,e)}(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){const r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}}let ix=(()=>{var e;class t extends Qv{constructor(r){super(r)}supports(r){return!0}addEventListener(r,o,i){return r.addEventListener(o,i,!1),()=>this.removeEventListener(r,o,i)}removeEventListener(r,o,i){return r.removeEventListener(o,i)}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Wn))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();const ny=["alt","control","meta","shift"],sx={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},ax={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};let lx=(()=>{var e;class t extends Qv{constructor(r){super(r)}supports(r){return null!=t.parseEventName(r)}addEventListener(r,o,i){const s=t.parseEventName(o),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Wo().onAndCancel(r,s.domEventName,a))}static parseEventName(r){const o=r.toLowerCase().split("."),i=o.shift();if(0===o.length||"keydown"!==i&&"keyup"!==i)return null;const s=t._normalizeKey(o.pop());let a="",l=o.indexOf("code");if(l>-1&&(o.splice(l,1),a="code."),ny.forEach(u=>{const d=o.indexOf(u);d>-1&&(o.splice(d,1),a+=u+".")}),a+=s,0!=o.length||0===s.length)return null;const c={};return c.domEventName=i,c.fullKey=a,c}static matchEventFullKeyCode(r,o){let i=sx[r.key]||r.key,s="";return o.indexOf("code.")>-1&&(i=r.code,s="code."),!(null==i||!i)&&(i=i.toLowerCase()," "===i?i="space":"."===i&&(i="dot"),ny.forEach(a=>{a!==i&&(0,ax[a])(r)&&(s+=a+".")}),s+=i,s===o)}static eventCallback(r,o,i){return s=>{t.matchEventFullKeyCode(s,r)&&i.runGuarded(()=>o(s))}}static _normalizeKey(r){return"esc"===r?"escape":r}}return(e=t).\u0275fac=function(r){return new(r||e)(J(Wn))},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})();const fx=ev(zA,"browser",[{provide:wr,useValue:"browser"},{provide:hp,useValue:function cx(){Ou.makeCurrent()},multi:!0},{provide:Wn,useFactory:function dx(){return function $b(e){Rl=e}(document),document},deps:[]}]),hx=new R(""),iy=[{provide:Ls,useClass:class QN{addToWindow(t){de.getAngularTestability=(r,o=!0)=>{const i=t.findTestabilityInTree(r,o);if(null==i)throw new S(5103,!1);return i},de.getAllAngularTestabilities=()=>t.getAllTestabilities(),de.getAllAngularRootElements=()=>t.getAllRootElements(),de.frameworkStabilizers||(de.frameworkStabilizers=[]),de.frameworkStabilizers.push(r=>{const o=de.getAllAngularTestabilities();let i=o.length,s=!1;const a=function(l){s=s||l,i--,0==i&&r(s)};o.forEach(l=>{l.whenStable(a)})})}findTestabilityInTree(t,n,r){return null==n?null:t.getTestability(n)??(r?Wo().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},deps:[]},{provide:Q_,useClass:ou,deps:[Ce,iu,Ls]},{provide:ou,useClass:ou,deps:[Ce,iu,Ls]}],sy=[{provide:$l,useValue:"root"},{provide:tn,useFactory:function ux(){return new tn},deps:[]},{provide:Ru,useClass:ix,multi:!0,deps:[Wn,Ce,wr]},{provide:Ru,useClass:lx,multi:!0,deps:[Wn]},Xv,Yv,Zv,{provide:Cp,useExisting:Xv},{provide:class bN{},useClass:YN,deps:[]},[]];let px=(()=>{var e;class t{constructor(r){}static withServerTransition(r){return{ngModule:t,providers:[{provide:os,useValue:r.appId}]}}}return(e=t).\u0275fac=function(r){return new(r||e)(J(hx,12))},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({providers:[...sy,...iy],imports:[vN,GA]}),t})();typeof window<"u"&&window;const{isArray:Dx}=Array,{getPrototypeOf:wx,prototype:bx,keys:Ex}=Object;const{isArray:Sx}=Array;function Nx(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function xx(...e){const t=function HC(e){return _e(Ea(e))?e.pop():void 0}(e),{args:n,keys:r}=function Ix(e){if(1===e.length){const t=e[0];if(Dx(t))return{args:t,keys:null};if(function Mx(e){return e&&"object"==typeof e&&wx(e)===bx}(t)){const n=Ex(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}(e),o=new je(i=>{const{length:s}=n;if(!s)return void i.complete();const a=new Array(s);let l=s,c=s;for(let u=0;u<s;u++){let d=!1;dn(n[u]).subscribe(new zt(i,h=>{d||(d=!0,c--),a[u]=h},()=>l--,void 0,()=>{(!l||!d)&&(c||i.next(r?Nx(r,a):a),i.complete())}))}});return t?o.pipe(function Tx(e){return Da(t=>function Ax(e,t){return Sx(t)?e(...t):e(t)}(e,t))}(t)):o}let uy=(()=>{var e;class t{constructor(r,o){this._renderer=r,this._elementRef=o,this.onChange=i=>{},this.onTouched=()=>{}}setProperty(r,o){this._renderer.setProperty(this._elementRef.nativeElement,r,o)}registerOnTouched(r){this.onTouched=r}registerOnChange(r){this.onChange=r}setDisabledState(r){this.setProperty("disabled",r)}}return(e=t).\u0275fac=function(r){return new(r||e)(M(jn),M(bt))},e.\u0275dir=j({type:e}),t})(),Qn=(()=>{var e;class t extends uy{}return(e=t).\u0275fac=function(){let n;return function(o){return(n||(n=Le(e)))(o||e)}}(),e.\u0275dir=j({type:e,features:[ae]}),t})();const Ut=new R("NgValueAccessor"),Ox={provide:Ut,useExisting:ue(()=>Bu),multi:!0};let Bu=(()=>{var e;class t extends Qn{writeValue(r){this.setProperty("checked",r)}}return(e=t).\u0275fac=function(){let n;return function(o){return(n||(n=Le(e)))(o||e)}}(),e.\u0275dir=j({type:e,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(r,o){1&r&&F("change",function(s){return o.onChange(s.target.checked)})("blur",function(){return o.onTouched()})},features:[me([Ox]),ae]}),t})();const Rx={provide:Ut,useExisting:ue(()=>Xo),multi:!0},Px=new R("CompositionEventMode");let Xo=(()=>{var e;class t extends uy{constructor(r,o,i){super(r,o),this._compositionMode=i,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function Fx(){const e=Wo()?Wo().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(r){this.setProperty("value",r??"")}_handleInput(r){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(r)}_compositionStart(){this._composing=!0}_compositionEnd(r){this._composing=!1,this._compositionMode&&this.onChange(r)}}return(e=t).\u0275fac=function(r){return new(r||e)(M(jn),M(bt),M(Px,8))},e.\u0275dir=j({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){1&r&&F("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},features:[me([Rx]),ae]}),t})();const Be=new R("NgValidators"),An=new R("NgAsyncValidators");function Cy(e){return null!=e}function Dy(e){return Es(e)?fi(e):e}function wy(e){let t={};return e.forEach(n=>{t=null!=n?{...t,...n}:t}),0===Object.keys(t).length?null:t}function by(e,t){return t.map(n=>n(e))}function Ey(e){return e.map(t=>function Lx(e){return!e.validate}(t)?t:n=>t.validate(n))}function ju(e){return null!=e?function Iy(e){if(!e)return null;const t=e.filter(Cy);return 0==t.length?null:function(n){return wy(by(n,t))}}(Ey(e)):null}function $u(e){return null!=e?function My(e){if(!e)return null;const t=e.filter(Cy);return 0==t.length?null:function(n){return xx(by(n,t).map(Dy)).pipe(Da(wy))}}(Ey(e)):null}function Sy(e,t){return null===e?[t]:Array.isArray(e)?[...e,t]:[e,t]}function Uu(e){return e?Array.isArray(e)?e:[e]:[]}function ra(e,t){return Array.isArray(e)?e.includes(t):e===t}function Ny(e,t){const n=Uu(t);return Uu(e).forEach(o=>{ra(n,o)||n.push(o)}),n}function xy(e,t){return Uu(t).filter(n=>!ra(e,n))}class Oy{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=ju(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=$u(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return!!this.control&&this.control.hasError(t,n)}getError(t,n){return this.control?this.control.getError(t,n):null}}class Ye extends Oy{get formDirective(){return null}get path(){return null}}class Tn extends Oy{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class Ry{constructor(t){this._cd=t}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let oa=(()=>{var e;class t extends Ry{constructor(r){super(r)}}return(e=t).\u0275fac=function(r){return new(r||e)(M(Tn,2))},e.\u0275dir=j({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){2&r&&Ms("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[ae]}),t})();const ei="VALID",sa="INVALID",Gr="PENDING",ti="DISABLED";function aa(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class Ly{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get valid(){return this.status===ei}get invalid(){return this.status===sa}get pending(){return this.status==Gr}get disabled(){return this.status===ti}get enabled(){return this.status!==ti}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Ny(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Ny(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(xy(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(xy(t,this._rawAsyncValidators))}hasValidator(t){return ra(this._rawValidators,t)}hasAsyncValidator(t){return ra(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){this.touched=!0,this._parent&&!t.onlySelf&&this._parent.markAsTouched(t)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(t=>t.markAllAsTouched())}markAsUntouched(t={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}markAsDirty(t={}){this.pristine=!1,this._parent&&!t.onlySelf&&this._parent.markAsDirty(t)}markAsPristine(t={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(n=>{n.markAsPristine({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}markAsPending(t={}){this.status=Gr,!1!==t.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!t.onlySelf&&this._parent.markAsPending(t)}disable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=ti,this.errors=null,this._forEachChild(r=>{r.disable({...t,onlySelf:!0})}),this._updateValue(),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=ei,this._forEachChild(r=>{r.enable({...t,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===ei||this.status===Gr)&&this._runAsyncValidator(t.emitEvent)),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(t)}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ti:ei}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t){if(this.asyncValidator){this.status=Gr,this._hasOwnPendingAsyncValidator=!0;const n=Dy(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(r=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(r,{emitEvent:t})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(!1!==n.emitEvent)}get(t){let n=t;return null==n||(Array.isArray(n)||(n=n.split(".")),0===n.length)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){const r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(t)}_initObservables(){this.valueChanges=new Ie,this.statusChanges=new Ie}_calculateStatus(){return this._allControlsDisabled()?ti:this.errors?sa:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Gr)?Gr:this._anyControlsHaveStatus(sa)?sa:ei}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t={}){this.pristine=!this._anyControlsDirty(),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}_updateTouched(t={}){this.touched=this._anyControlsTouched(),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){aa(t)&&null!=t.updateOn&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){return!t&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=function $x(e){return Array.isArray(e)?ju(e):e||null}(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=function Ux(e){return Array.isArray(e)?$u(e):e||null}(this._rawAsyncValidators)}}const qr=new R("CallSetDisabledState",{providedIn:"root",factory:()=>la}),la="always";function ni(e,t,n=la){(function Qu(e,t){const n=function Ay(e){return e._rawValidators}(e);null!==t.validator?e.setValidators(Sy(n,t.validator)):"function"==typeof n&&e.setValidators([n]);const r=function Ty(e){return e._rawAsyncValidators}(e);null!==t.asyncValidator?e.setAsyncValidators(Sy(r,t.asyncValidator)):"function"==typeof r&&e.setAsyncValidators([r]);const o=()=>e.updateValueAndValidity();da(t._rawValidators,o),da(t._rawAsyncValidators,o)})(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||"always"===n)&&t.valueAccessor.setDisabledState?.(e.disabled),function qx(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&Vy(e,t)})}(e,t),function Zx(e,t){const n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}(e,t),function Wx(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&Vy(e,t),"submit"!==e.updateOn&&e.markAsTouched()})}(e,t),function Gx(e,t){if(t.valueAccessor.setDisabledState){const n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}(e,t)}function da(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function Vy(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function jy(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function $y(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}const Uy=class extends Ly{constructor(t=null,n,r){super(function qu(e){return(aa(e)?e.validators:e)||null}(n),function Wu(e,t){return(aa(t)?t.asyncValidators:e)||null}(r,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),aa(n)&&(n.nonNullable||n.initialValueIsDefault)&&(this.defaultValue=$y(t)?t.value:t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&!1!==n.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==n.emitViewToModelChange)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){jy(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){jy(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(t){$y(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}},nO={provide:Tn,useExisting:ue(()=>oi)},qy=(()=>Promise.resolve())();let oi=(()=>{var e;class t extends Tn{constructor(r,o,i,s,a,l){super(),this._changeDetectorRef=a,this.callSetDisabledState=l,this.control=new Uy,this._registered=!1,this.name="",this.update=new Ie,this._parent=r,this._setValidators(o),this._setAsyncValidators(i),this.valueAccessor=function Ju(e,t){if(!t)return null;let n,r,o;return Array.isArray(t),t.forEach(i=>{i.constructor===Xo?n=i:function Kx(e){return Object.getPrototypeOf(e.constructor)===Qn}(i)?r=i:o=i}),o||r||n||null}(0,s)}ngOnChanges(r){if(this._checkForErrors(),!this._registered||"name"in r){if(this._registered&&(this._checkName(),this.formDirective)){const o=r.name.previousValue;this.formDirective.removeControl({name:o,path:this._getPath(o)})}this._setUpControl()}"isDisabled"in r&&this._updateDisabled(r),function Ku(e,t){if(!e.hasOwnProperty("model"))return!1;const n=e.model;return!!n.isFirstChange()||!Object.is(t,n.currentValue)}(r,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(r){this.viewModel=r,this.update.emit(r)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){ni(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(r){qy.then(()=>{this.control.setValue(r,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(r){const o=r.isDisabled.currentValue,i=0!==o&&function pu(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}(o);qy.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(r){return this._parent?function ca(e,t){return[...t.path,e]}(r,this._parent):[r]}}return(e=t).\u0275fac=function(r){return new(r||e)(M(Ye,9),M(Be,10),M(An,10),M(Ut,10),M(uv,8),M(qr,8))},e.\u0275dir=j({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[me([nO]),ae,Yt]}),t})(),Zy=(()=>{var e;class t{}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({}),t})();const aO={provide:Ut,useExisting:ue(()=>ed),multi:!0};let ed=(()=>{var e;class t extends Qn{writeValue(r){this.setProperty("value",parseFloat(r))}registerOnChange(r){this.onChange=o=>{r(""==o?null:parseFloat(o))}}}return(e=t).\u0275fac=function(){let n;return function(o){return(n||(n=Le(e)))(o||e)}}(),e.\u0275dir=j({type:e,selectors:[["input","type","range","formControlName",""],["input","type","range","formControl",""],["input","type","range","ngModel",""]],hostBindings:function(r,o){1&r&&F("change",function(s){return o.onChange(s.target.value)})("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},features:[me([aO]),ae]}),t})();const hO={provide:Ut,useExisting:ue(()=>ii),multi:!0};function eC(e,t){return null==e?`${t}`:(t&&"object"==typeof t&&(t="Object"),`${e}: ${t}`.slice(0,50))}let ii=(()=>{var e;class t extends Qn{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(r){this._compareWith=r}writeValue(r){this.value=r;const i=eC(this._getOptionId(r),r);this.setProperty("value",i)}registerOnChange(r){this.onChange=o=>{this.value=this._getOptionValue(o),r(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(r){for(const o of this._optionMap.keys())if(this._compareWith(this._optionMap.get(o),r))return o;return null}_getOptionValue(r){const o=function pO(e){return e.split(":")[0]}(r);return this._optionMap.has(o)?this._optionMap.get(o):r}}return(e=t).\u0275fac=function(){let n;return function(o){return(n||(n=Le(e)))(o||e)}}(),e.\u0275dir=j({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){1&r&&F("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},features:[me([hO]),ae]}),t})(),od=(()=>{var e;class t{constructor(r,o,i){this._element=r,this._renderer=o,this._select=i,this._select&&(this.id=this._select._registerOption())}set ngValue(r){null!=this._select&&(this._select._optionMap.set(this.id,r),this._setElementValue(eC(this.id,r)),this._select.writeValue(this._select.value))}set value(r){this._setElementValue(r),this._select&&this._select.writeValue(this._select.value)}_setElementValue(r){this._renderer.setProperty(this._element.nativeElement,"value",r)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}}return(e=t).\u0275fac=function(r){return new(r||e)(M(bt),M(jn),M(ii,9))},e.\u0275dir=j({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}}),t})();const gO={provide:Ut,useExisting:ue(()=>id),multi:!0};function tC(e,t){return null==e?`${t}`:("string"==typeof t&&(t=`'${t}'`),t&&"object"==typeof t&&(t="Object"),`${e}: ${t}`.slice(0,50))}let id=(()=>{var e;class t extends Qn{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(r){this._compareWith=r}writeValue(r){let o;if(this.value=r,Array.isArray(r)){const i=r.map(s=>this._getOptionId(s));o=(s,a)=>{s._setSelected(i.indexOf(a.toString())>-1)}}else o=(i,s)=>{i._setSelected(!1)};this._optionMap.forEach(o)}registerOnChange(r){this.onChange=o=>{const i=[],s=o.selectedOptions;if(void 0!==s){const a=s;for(let l=0;l<a.length;l++){const u=this._getOptionValue(a[l].value);i.push(u)}}else{const a=o.options;for(let l=0;l<a.length;l++){const c=a[l];if(c.selected){const u=this._getOptionValue(c.value);i.push(u)}}}this.value=i,r(i)}}_registerOption(r){const o=(this._idCounter++).toString();return this._optionMap.set(o,r),o}_getOptionId(r){for(const o of this._optionMap.keys())if(this._compareWith(this._optionMap.get(o)._value,r))return o;return null}_getOptionValue(r){const o=function mO(e){return e.split(":")[0]}(r);return this._optionMap.has(o)?this._optionMap.get(o)._value:r}}return(e=t).\u0275fac=function(){let n;return function(o){return(n||(n=Le(e)))(o||e)}}(),e.\u0275dir=j({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){1&r&&F("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},features:[me([gO]),ae]}),t})(),sd=(()=>{var e;class t{constructor(r,o,i){this._element=r,this._renderer=o,this._select=i,this._select&&(this.id=this._select._registerOption(this))}set ngValue(r){null!=this._select&&(this._value=r,this._setElementValue(tC(this.id,r)),this._select.writeValue(this._select.value))}set value(r){this._select?(this._value=r,this._setElementValue(tC(this.id,r)),this._select.writeValue(this._select.value)):this._setElementValue(r)}_setElementValue(r){this._renderer.setProperty(this._element.nativeElement,"value",r)}_setSelected(r){this._renderer.setProperty(this._element.nativeElement,"selected",r)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}}return(e=t).\u0275fac=function(r){return new(r||e)(M(bt),M(jn),M(id,9))},e.\u0275dir=j({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}}),t})(),IO=(()=>{var e;class t{}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({imports:[Zy]}),t})(),SO=(()=>{var e;class t{static withConfig(r){return{ngModule:t,providers:[{provide:qr,useValue:r.callSetDisabledState??la}]}}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275mod=pn({type:e}),e.\u0275inj=Gt({imports:[IO]}),t})();class dC{constructor(){this.riskHotspotsSettings=null,this.coverageInfoSettings=null}}class AO{constructor(){this.showLineCoverage=!0,this.showBranchCoverage=!0,this.showMethodCoverage=!0,this.visibleMetrics=[],this.groupingMaximum=0,this.grouping=0,this.historyComparisionDate="",this.historyComparisionType="",this.filter="",this.sortBy="name",this.sortOrder="asc",this.collapseStates=[]}}class TO{constructor(t){this.et="",this.et=t.et,this.cl=t.cl,this.ucl=t.ucl,this.cal=t.cal,this.tl=t.tl,this.lcq=t.lcq,this.cb=t.cb,this.tb=t.tb,this.bcq=t.bcq,this.cm=t.cm,this.tm=t.tm,this.mcq=t.mcq}get coverageRatioText(){return 0===this.tl?"-":this.cl+"/"+this.cal}get branchCoverageRatioText(){return 0===this.tb?"-":this.cb+"/"+this.tb}get methodCoverageRatioText(){return 0===this.tm?"-":this.cm+"/"+this.tm}}class vt{static roundNumber(t){return Math.floor(t*Math.pow(10,vt.maximumDecimalPlacesForCoverageQuotas))/Math.pow(10,vt.maximumDecimalPlacesForCoverageQuotas)}static getNthOrLastIndexOf(t,n,r){let o=0,i=-1,s=-1;for(;o<r&&(s=t.indexOf(n,i+1),-1!==s);)i=s,o++;return i}}class fC{constructor(){this.name="",this.coveredLines=0,this.uncoveredLines=0,this.coverableLines=0,this.totalLines=0,this.coveredBranches=0,this.totalBranches=0,this.coveredMethods=0,this.totalMethods=0}get coverage(){return 0===this.coverableLines?NaN:vt.roundNumber(100*this.coveredLines/this.coverableLines)}get coveragePercentage(){return 0===this.coverableLines?"":this.coverage+"%"}get coverageRatioText(){return 0===this.coverableLines?"-":this.coveredLines+"/"+this.coverableLines}get branchCoverage(){return 0===this.totalBranches?NaN:vt.roundNumber(100*this.coveredBranches/this.totalBranches)}get branchCoveragePercentage(){return 0===this.totalBranches?"":this.branchCoverage+"%"}get branchCoverageRatioText(){return 0===this.totalBranches?"-":this.coveredBranches+"/"+this.totalBranches}get methodCoverage(){return 0===this.totalMethods?NaN:vt.roundNumber(100*this.coveredMethods/this.totalMethods)}get methodCoveragePercentage(){return 0===this.totalMethods?"":this.methodCoverage+"%"}get methodCoverageRatioText(){return 0===this.totalMethods?"-":this.coveredMethods+"/"+this.totalMethods}}class ld extends fC{constructor(t,n){super(),this.reportPath="",this.lineCoverageHistory=[],this.branchCoverageHistory=[],this.methodCoverageHistory=[],this.historicCoverages=[],this.currentHistoricCoverage=null,this.name=t.name,this.reportPath=t.rp?t.rp+n:t.rp,this.coveredLines=t.cl,this.uncoveredLines=t.ucl,this.coverableLines=t.cal,this.totalLines=t.tl,this.coveredBranches=t.cb,this.totalBranches=t.tb,this.coveredMethods=t.cm,this.totalMethods=t.tm,this.lineCoverageHistory=t.lch,this.branchCoverageHistory=t.bch,this.methodCoverageHistory=t.mch,t.hc.forEach(r=>{this.historicCoverages.push(new TO(r))}),this.metrics=t.metrics}get coverage(){return 0===this.coverableLines?NaN:vt.roundNumber(100*this.coveredLines/this.coverableLines)}visible(t,n){if(""!==t&&-1===this.name.toLowerCase().indexOf(t.toLowerCase()))return!1;if(""===n||null===this.currentHistoricCoverage)return!0;if("allChanges"===n){if(this.coveredLines===this.currentHistoricCoverage.cl&&this.uncoveredLines===this.currentHistoricCoverage.ucl&&this.coverableLines===this.currentHistoricCoverage.cal&&this.totalLines===this.currentHistoricCoverage.tl&&this.coveredBranches===this.currentHistoricCoverage.cb&&this.totalBranches===this.currentHistoricCoverage.tb&&this.coveredMethods===this.currentHistoricCoverage.cm&&this.totalMethods===this.currentHistoricCoverage.tm)return!1}else if("lineCoverageIncreaseOnly"===n){let r=this.coverage;if(isNaN(r)||r<=this.currentHistoricCoverage.lcq)return!1}else if("lineCoverageDecreaseOnly"===n){let r=this.coverage;if(isNaN(r)||r>=this.currentHistoricCoverage.lcq)return!1}else if("branchCoverageIncreaseOnly"===n){let r=this.branchCoverage;if(isNaN(r)||r<=this.currentHistoricCoverage.bcq)return!1}else if("branchCoverageDecreaseOnly"===n){let r=this.branchCoverage;if(isNaN(r)||r>=this.currentHistoricCoverage.bcq)return!1}else if("methodCoverageIncreaseOnly"===n){let r=this.methodCoverage;if(isNaN(r)||r<=this.currentHistoricCoverage.mcq)return!1}else if("methodCoverageDecreaseOnly"===n){let r=this.methodCoverage;if(isNaN(r)||r>=this.currentHistoricCoverage.mcq)return!1}return!0}updateCurrentHistoricCoverage(t){if(this.currentHistoricCoverage=null,""!==t)for(let n=0;n<this.historicCoverages.length;n++)if(this.historicCoverages[n].et===t){this.currentHistoricCoverage=this.historicCoverages[n];break}}}class Nn extends fC{constructor(t,n){super(),this.subElements=[],this.classes=[],this.collapsed=!1,this.name=t,this.collapsed=t.indexOf("Test")>-1&&null===n}visible(t,n){if(""!==t&&this.name.toLowerCase().indexOf(t.toLowerCase())>-1)return!0;for(let r=0;r<this.subElements.length;r++)if(this.subElements[r].visible(t,n))return!0;for(let r=0;r<this.classes.length;r++)if(this.classes[r].visible(t,n))return!0;return!1}insertClass(t,n){if(this.coveredLines+=t.coveredLines,this.uncoveredLines+=t.uncoveredLines,this.coverableLines+=t.coverableLines,this.totalLines+=t.totalLines,this.coveredBranches+=t.coveredBranches,this.totalBranches+=t.totalBranches,this.coveredMethods+=t.coveredMethods,this.totalMethods+=t.totalMethods,null===n)return void this.classes.push(t);let r=vt.getNthOrLastIndexOf(t.name,".",n);-1===r&&(r=vt.getNthOrLastIndexOf(t.name,"\\",n));let o=-1===r?"-":t.name.substring(0,r);for(let s=0;s<this.subElements.length;s++)if(this.subElements[s].name===o)return void this.subElements[s].insertClass(t,null);let i=new Nn(o,this);this.subElements.push(i),i.insertClass(t,null)}collapse(){this.collapsed=!0;for(let t=0;t<this.subElements.length;t++)this.subElements[t].collapse()}expand(){this.collapsed=!1;for(let t=0;t<this.subElements.length;t++)this.subElements[t].expand()}toggleCollapse(t){t.preventDefault(),this.collapsed=!this.collapsed}updateCurrentHistoricCoverage(t){for(let n=0;n<this.subElements.length;n++)this.subElements[n].updateCurrentHistoricCoverage(t);for(let n=0;n<this.classes.length;n++)this.classes[n].updateCurrentHistoricCoverage(t)}static sortCodeElementViewModels(t,n,r){let o=r?-1:1,i=r?1:-1;"name"===n?t.sort(function(s,a){return s.name===a.name?0:s.name<a.name?o:i}):"covered"===n?t.sort(function(s,a){return s.coveredLines===a.coveredLines?0:s.coveredLines<a.coveredLines?o:i}):"uncovered"===n?t.sort(function(s,a){return s.uncoveredLines===a.uncoveredLines?0:s.uncoveredLines<a.uncoveredLines?o:i}):"coverable"===n?t.sort(function(s,a){return s.coverableLines===a.coverableLines?0:s.coverableLines<a.coverableLines?o:i}):"total"===n?t.sort(function(s,a){return s.totalLines===a.totalLines?0:s.totalLines<a.totalLines?o:i}):"coverage"===n?t.sort(function(s,a){return s.coverage===a.coverage?0:isNaN(s.coverage)?o:isNaN(a.coverage)?i:s.coverage<a.coverage?o:i}):"branchcoverage"===n?t.sort(function(s,a){return s.branchCoverage===a.branchCoverage?0:isNaN(s.branchCoverage)?o:isNaN(a.branchCoverage)?i:s.branchCoverage<a.branchCoverage?o:i}):"methodcoverage"===n&&t.sort(function(s,a){return s.methodCoverage===a.methodCoverage?0:isNaN(s.methodCoverage)?o:isNaN(a.methodCoverage)?i:s.methodCoverage<a.methodCoverage?o:i})}changeSorting(t,n){Nn.sortCodeElementViewModels(this.subElements,t,n);let r=n?-1:1,o=n?1:-1;this.classes.sort("name"===t?function(i,s){return i.name===s.name?0:i.name<s.name?r:o}:"covered"===t?function(i,s){return i.coveredLines===s.coveredLines?0:i.coveredLines<s.coveredLines?r:o}:"uncovered"===t?function(i,s){return i.uncoveredLines===s.uncoveredLines?0:i.uncoveredLines<s.uncoveredLines?r:o}:"coverable"===t?function(i,s){return i.coverableLines===s.coverableLines?0:i.coverableLines<s.coverableLines?r:o}:"total"===t?function(i,s){return i.totalLines===s.totalLines?0:i.totalLines<s.totalLines?r:o}:"coverage"===t?function(i,s){return i.coverage===s.coverage?0:isNaN(i.coverage)?r:isNaN(s.coverage)?o:i.coverage<s.coverage?r:o}:"covered_branches"===t?function(i,s){return i.coveredBranches===s.coveredBranches?0:i.coveredBranches<s.coveredBranches?r:o}:"total_branches"===t?function(i,s){return i.totalBranches===s.totalBranches?0:i.totalBranches<s.totalBranches?r:o}:"branchcoverage"===t?function(i,s){return i.branchCoverage===s.branchCoverage?0:isNaN(i.branchCoverage)?r:isNaN(s.branchCoverage)?o:i.branchCoverage<s.branchCoverage?r:o}:"covered_methods"===t?function(i,s){return i.coveredMethods===s.coveredMethods?0:i.coveredMethods<s.coveredMethods?r:o}:"total_methods"===t?function(i,s){return i.totalMethods===s.totalMethods?0:i.totalMethods<s.totalMethods?r:o}:"methodcoverage"===t?function(i,s){return i.methodCoverage===s.methodCoverage?0:isNaN(i.methodCoverage)?r:isNaN(s.methodCoverage)?o:i.methodCoverage<s.methodCoverage?r:o}:function(i,s){const a=i.metrics[t],l=s.metrics[t];return a===l?0:isNaN(a)?r:isNaN(l)?o:a<l?r:o});for(let i=0;i<this.subElements.length;i++)this.subElements[i].changeSorting(t,n)}}let cd=(()=>{var e;class t{get nativeWindow(){return function NO(){return window}()}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275prov=se({token:e,factory:e.\u0275fac}),t})(),xO=(()=>{var e;class t{constructor(){this.translations={}}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["pro-button"]],inputs:{translations:"translations"},decls:3,vars:1,consts:[["href","https://reportgenerator.io/pro","target","_blank",1,"pro-button","pro-button-tiny",3,"title"]],template:function(r,o){1&r&&(b(0,"\xa0"),y(1,"a",0),b(2,"PRO"),v()),2&r&&(f(1),bn("title",o.translations.methodCoverageProVersion))},encapsulation:2}),t})();function OO(e,t){if(1&e){const n=De();y(0,"div",3)(1,"label")(2,"input",4),F("ngModelChange",function(o){return G(n),q(_().showBranchCoverage=o)})("change",function(){G(n);const o=_();return q(o.showBranchCoverageChange.emit(o.showBranchCoverage))}),v(),b(3),v()()}if(2&e){const n=_();f(2),g("ngModel",n.showBranchCoverage),f(1),z(" ",n.translations.branchCoverage,"")}}function RO(e,t){1&e&&N(0,"pro-button",9),2&e&&g("translations",_().translations)}function FO(e,t){1&e&&N(0,"pro-button",9),2&e&&g("translations",_(2).translations)}function PO(e,t){1&e&&(y(0,"a",13),N(1,"i",14),v()),2&e&&g("href",_().$implicit.explanationUrl,wn)}function kO(e,t){if(1&e){const n=De();y(0,"div",3)(1,"label")(2,"input",11),F("change",function(){const i=G(n).$implicit;return q(_(2).toggleMetric(i))}),v(),b(3),v(),b(4,"\xa0"),C(5,PO,2,1,"a",12),v()}if(2&e){const n=t.$implicit,r=_(2);f(2),g("checked",r.isMetricSelected(n))("disabled",!r.methodCoverageAvailable),f(1),z(" ",n.name,""),f(2),g("ngIf",n.explanationUrl)}}function LO(e,t){if(1&e&&(Z(0),N(1,"br")(2,"br"),y(3,"b"),b(4),v(),C(5,FO,1,1,"pro-button",7),C(6,kO,6,4,"div",10),Q()),2&e){const n=_();f(4),x(n.translations.metrics),f(1),g("ngIf",!n.methodCoverageAvailable),f(1),g("ngForOf",n.metrics)}}let VO=(()=>{var e;class t{constructor(){this.visible=!1,this.visibleChange=new Ie,this.translations={},this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.metrics=[],this.showLineCoverage=!1,this.showLineCoverageChange=new Ie,this.showBranchCoverage=!1,this.showBranchCoverageChange=new Ie,this.showMethodCoverage=!1,this.showMethodCoverageChange=new Ie,this.visibleMetrics=[],this.visibleMetricsChange=new Ie}isMetricSelected(r){return void 0!==this.visibleMetrics.find(o=>o.name===r.name)}toggleMetric(r){let o=this.visibleMetrics.find(i=>i.name===r.name);o?this.visibleMetrics.splice(this.visibleMetrics.indexOf(o),1):this.visibleMetrics.push(r),this.visibleMetrics=[...this.visibleMetrics],this.visibleMetricsChange.emit(this.visibleMetrics)}close(){this.visible=!1,this.visibleChange.emit(this.visible)}cancelEvent(r){r.stopPropagation()}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["popup"]],inputs:{visible:"visible",translations:"translations",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",metrics:"metrics",showLineCoverage:"showLineCoverage",showBranchCoverage:"showBranchCoverage",showMethodCoverage:"showMethodCoverage",visibleMetrics:"visibleMetrics"},outputs:{visibleChange:"visibleChange",showLineCoverageChange:"showLineCoverageChange",showBranchCoverageChange:"showBranchCoverageChange",showMethodCoverageChange:"showMethodCoverageChange",visibleMetricsChange:"visibleMetricsChange"},decls:17,vars:9,consts:[[1,"popup-container",3,"click"],[1,"popup",3,"click"],[1,"close",3,"click"],[1,"mt-1"],["type","checkbox",3,"ngModel","ngModelChange","change"],["class","mt-1",4,"ngIf"],["type","checkbox",3,"ngModel","disabled","ngModelChange","change"],[3,"translations",4,"ngIf"],[4,"ngIf"],[3,"translations"],["class","mt-1",4,"ngFor","ngForOf"],["type","checkbox",3,"checked","disabled","change"],["target","_blank",3,"href",4,"ngIf"],["target","_blank",3,"href"],[1,"icon-info-circled"]],template:function(r,o){1&r&&(y(0,"div",0),F("click",function(){return o.close()}),y(1,"div",1),F("click",function(s){return o.cancelEvent(s)}),y(2,"div",2),F("click",function(){return o.close()}),b(3,"X"),v(),y(4,"b"),b(5),v(),y(6,"div",3)(7,"label")(8,"input",4),F("ngModelChange",function(s){return o.showLineCoverage=s})("change",function(){return o.showLineCoverageChange.emit(o.showLineCoverage)}),v(),b(9),v()(),C(10,OO,4,2,"div",5),y(11,"div",3)(12,"label")(13,"input",6),F("ngModelChange",function(s){return o.showMethodCoverage=s})("change",function(){return o.showMethodCoverageChange.emit(o.showMethodCoverage)}),v(),b(14),v(),C(15,RO,1,1,"pro-button",7),v(),C(16,LO,7,3,"ng-container",8),v()()),2&r&&(f(5),x(o.translations.coverageTypes),f(3),g("ngModel",o.showLineCoverage),f(1),z(" ",o.translations.coverage,""),f(1),g("ngIf",o.branchCoverageAvailable),f(3),g("ngModel",o.showMethodCoverage)("disabled",!o.methodCoverageAvailable),f(1),z(" ",o.translations.methodCoverage,""),f(1),g("ngIf",!o.methodCoverageAvailable),f(1),g("ngIf",o.metrics.length>0))},dependencies:[zr,Zn,Bu,oa,oi,xO],encapsulation:2}),t})();function HO(e,t){1&e&&N(0,"td",3)}function BO(e,t){1&e&&N(0,"td"),2&e&&jt("green ",_().greenClass,"")}function jO(e,t){1&e&&N(0,"td"),2&e&&jt("red ",_().redClass,"")}let hC=(()=>{var e;class t{constructor(){this.grayVisible=!0,this.greenVisible=!1,this.redVisible=!1,this.greenClass="",this.redClass="",this._percentage=NaN}get percentage(){return this._percentage}set percentage(r){this._percentage=r,this.grayVisible=isNaN(r),this.greenVisible=!isNaN(r)&&Math.round(r)>0,this.redVisible=!isNaN(r)&&100-Math.round(r)>0,this.greenClass="covered"+Math.round(r),this.redClass="covered"+(100-Math.round(r))}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["coverage-bar"]],inputs:{percentage:"percentage"},decls:4,vars:3,consts:[[1,"coverage"],["class","gray covered100",4,"ngIf"],[3,"class",4,"ngIf"],[1,"gray","covered100"]],template:function(r,o){1&r&&(y(0,"table",0),C(1,HO,1,0,"td",1),C(2,BO,1,3,"td",2),C(3,jO,1,3,"td",2),v()),2&r&&(f(1),g("ngIf",o.grayVisible),f(1),g("ngIf",o.greenVisible),f(1),g("ngIf",o.redVisible))},dependencies:[Zn],encapsulation:2,changeDetection:0}),t})();const $O=["codeelement-row",""];function UO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.coveredLines)}}function zO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.uncoveredLines)}}function GO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.coverableLines)}}function qO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.totalLines)}}function WO(e,t){if(1&e&&(y(0,"th",6),b(1),v()),2&e){const n=_();g("title",n.element.coverageRatioText),f(1),x(n.element.coveragePercentage)}}function ZO(e,t){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),v()),2&e){const n=_();f(1),g("percentage",n.element.coverage)}}function QO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.coveredBranches)}}function YO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.totalBranches)}}function KO(e,t){if(1&e&&(y(0,"th",6),b(1),v()),2&e){const n=_();g("title",n.element.branchCoverageRatioText),f(1),x(n.element.branchCoveragePercentage)}}function JO(e,t){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),v()),2&e){const n=_();f(1),g("percentage",n.element.branchCoverage)}}function XO(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.coveredMethods)}}function eR(e,t){if(1&e&&(y(0,"th",5),b(1),v()),2&e){const n=_();f(1),x(n.element.totalMethods)}}function tR(e,t){if(1&e&&(y(0,"th",6),b(1),v()),2&e){const n=_();g("title",n.element.methodCoverageRatioText),f(1),x(n.element.methodCoveragePercentage)}}function nR(e,t){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),v()),2&e){const n=_();f(1),g("percentage",n.element.methodCoverage)}}function rR(e,t){1&e&&N(0,"th",5)}const oR=function(e,t){return{"icon-plus":e,"icon-minus":t}};let iR=(()=>{var e;class t{constructor(){this.collapsed=!1,this.lineCoverageAvailable=!1,this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.visibleMetrics=[]}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["","codeelement-row",""]],inputs:{element:"element",collapsed:"collapsed",lineCoverageAvailable:"lineCoverageAvailable",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",visibleMetrics:"visibleMetrics"},attrs:$O,decls:19,vars:20,consts:[["href","#",3,"click"],[3,"ngClass"],["class","right",4,"ngIf"],["class","right",3,"title",4,"ngIf"],["class","right",4,"ngFor","ngForOf"],[1,"right"],[1,"right",3,"title"],[3,"percentage"]],template:function(r,o){1&r&&(y(0,"th")(1,"a",0),F("click",function(s){return o.element.toggleCollapse(s)}),N(2,"i",1),b(3),v()(),C(4,UO,2,1,"th",2),C(5,zO,2,1,"th",2),C(6,GO,2,1,"th",2),C(7,qO,2,1,"th",2),C(8,WO,2,2,"th",3),C(9,ZO,2,1,"th",2),C(10,QO,2,1,"th",2),C(11,YO,2,1,"th",2),C(12,KO,2,2,"th",3),C(13,JO,2,1,"th",2),C(14,XO,2,1,"th",2),C(15,eR,2,1,"th",2),C(16,tR,2,2,"th",3),C(17,nR,2,1,"th",2),C(18,rR,1,0,"th",4)),2&r&&(f(2),g("ngClass",Uc(17,oR,o.element.collapsed,!o.element.collapsed)),f(1),z(" ",o.element.name,""),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngForOf",o.visibleMetrics))},dependencies:[Yo,zr,Zn,hC],encapsulation:2,changeDetection:0}),t})();const sR=["coverage-history-chart",""];let aR=(()=>{var e;class t{constructor(){this.path=null,this._historicCoverages=[]}get historicCoverages(){return this._historicCoverages}set historicCoverages(r){if(this._historicCoverages=r,r.length>1){let o="";for(let i=0;i<r.length;i++)o+=0===i?"M":"L",o+=`${vt.roundNumber(30*i/(r.length-1))}`,o+=`,${vt.roundNumber(18-18*r[i]/100)}`;this.path=o}else this.path=null}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["","coverage-history-chart",""]],inputs:{historicCoverages:"historicCoverages"},attrs:sR,decls:3,vars:1,consts:[["width","30","height","18",1,"ct-chart-line"],[1,"ct-series","ct-series-a"],[1,"ct-line"]],template:function(r,o){1&r&&(function Zf(){k.lFrame.currentNamespace="svg"}(),y(0,"svg",0)(1,"g",1),N(2,"path",2),v()()),2&r&&(f(2),It("d",o.path))},encapsulation:2,changeDetection:0}),t})();const lR=["class-row",""];function cR(e,t){if(1&e&&(y(0,"a",5),b(1),v()),2&e){const n=_();g("href",n.clazz.reportPath,wn),f(1),x(n.clazz.name)}}function uR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_();f(1),x(n.clazz.name)}}function dR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.coveredLines,n.clazz.currentHistoricCoverage.cl),""),f(1),z(" ",n.clazz.coveredLines," "),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),z(" ",n.clazz.currentHistoricCoverage.cl," ")}}function fR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.coveredLines," ")}}function hR(e,t){if(1&e&&(y(0,"td",6),C(1,dR,5,6,"ng-container",1),C(2,fR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function pR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.currentHistoricCoverage.ucl,n.clazz.uncoveredLines),""),f(1),z(" ",n.clazz.uncoveredLines," "),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),z(" ",n.clazz.currentHistoricCoverage.ucl," ")}}function gR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.uncoveredLines," ")}}function mR(e,t){if(1&e&&(y(0,"td",6),C(1,pR,5,6,"ng-container",1),C(2,gR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function _R(e,t){if(1&e&&(Z(0),y(1,"div",8),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(2),x(n.clazz.coverableLines),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),x(n.clazz.currentHistoricCoverage.cal)}}function vR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.coverableLines," ")}}function yR(e,t){if(1&e&&(y(0,"td",6),C(1,_R,5,3,"ng-container",1),C(2,vR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function CR(e,t){if(1&e&&(Z(0),y(1,"div",8),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(2),x(n.clazz.totalLines),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),x(n.clazz.currentHistoricCoverage.tl)}}function DR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.totalLines," ")}}function wR(e,t){if(1&e&&(y(0,"td",6),C(1,CR,5,3,"ng-container",1),C(2,DR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}const ud=function(e){return{historiccoverageoffset:e}};function bR(e,t){if(1&e&&N(0,"div",11),2&e){const n=_(2);bn("title",n.translations.history+": "+n.translations.coverage),g("historicCoverages",n.clazz.lineCoverageHistory)("ngClass",Os(3,ud,null!==n.clazz.currentHistoricCoverage))}}function ER(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.coverage,n.clazz.currentHistoricCoverage.lcq),""),f(1),z(" ",n.clazz.coveragePercentage," "),f(1),g("title",n.clazz.currentHistoricCoverage.et+": "+n.clazz.currentHistoricCoverage.coverageRatioText),f(1),z("",n.clazz.currentHistoricCoverage.lcq,"%")}}function IR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.coveragePercentage," ")}}function MR(e,t){if(1&e&&(y(0,"td",9),C(1,bR,1,5,"div",10),C(2,ER,5,6,"ng-container",1),C(3,IR,2,1,"ng-container",1),v()),2&e){const n=_();g("title",n.clazz.coverageRatioText),f(1),g("ngIf",n.clazz.lineCoverageHistory.length>1),f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function SR(e,t){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),v()),2&e){const n=_();f(1),g("percentage",n.clazz.coverage)}}function AR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.coveredBranches,n.clazz.currentHistoricCoverage.cb),""),f(1),z(" ",n.clazz.coveredBranches," "),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),z(" ",n.clazz.currentHistoricCoverage.cb," ")}}function TR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.coveredBranches," ")}}function NR(e,t){if(1&e&&(y(0,"td",6),C(1,AR,5,6,"ng-container",1),C(2,TR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function xR(e,t){if(1&e&&(Z(0),y(1,"div",8),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(2),x(n.clazz.totalBranches),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),x(n.clazz.currentHistoricCoverage.tb)}}function OR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.totalBranches," ")}}function RR(e,t){if(1&e&&(y(0,"td",6),C(1,xR,5,3,"ng-container",1),C(2,OR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function FR(e,t){if(1&e&&N(0,"div",14),2&e){const n=_(2);bn("title",n.translations.history+": "+n.translations.branchCoverage),g("historicCoverages",n.clazz.branchCoverageHistory)("ngClass",Os(3,ud,null!==n.clazz.currentHistoricCoverage))}}function PR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.branchCoverage,n.clazz.currentHistoricCoverage.bcq),""),f(1),z(" ",n.clazz.branchCoveragePercentage," "),f(1),g("title",n.clazz.currentHistoricCoverage.et+": "+n.clazz.currentHistoricCoverage.branchCoverageRatioText),f(1),z("",n.clazz.currentHistoricCoverage.bcq,"%")}}function kR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.branchCoveragePercentage," ")}}function LR(e,t){if(1&e&&(y(0,"td",9),C(1,FR,1,5,"div",13),C(2,PR,5,6,"ng-container",1),C(3,kR,2,1,"ng-container",1),v()),2&e){const n=_();g("title",n.clazz.branchCoverageRatioText),f(1),g("ngIf",n.clazz.branchCoverageHistory.length>1),f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function VR(e,t){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),v()),2&e){const n=_();f(1),g("percentage",n.clazz.branchCoverage)}}function HR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.coveredMethods,n.clazz.currentHistoricCoverage.cm),""),f(1),z(" ",n.clazz.coveredMethods," "),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),z(" ",n.clazz.currentHistoricCoverage.cm," ")}}function BR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.coveredMethods," ")}}function jR(e,t){if(1&e&&(y(0,"td",6),C(1,HR,5,6,"ng-container",1),C(2,BR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function $R(e,t){if(1&e&&(Z(0),y(1,"div",8),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(2),x(n.clazz.totalMethods),f(1),g("title",n.clazz.currentHistoricCoverage.et),f(1),x(n.clazz.currentHistoricCoverage.tm)}}function UR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.totalMethods," ")}}function zR(e,t){if(1&e&&(y(0,"td",6),C(1,$R,5,3,"ng-container",1),C(2,UR,2,1,"ng-container",1),v()),2&e){const n=_();f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function GR(e,t){if(1&e&&N(0,"div",16),2&e){const n=_(2);bn("title",n.translations.history+": "+n.translations.methodCoverage),g("historicCoverages",n.clazz.methodCoverageHistory)("ngClass",Os(3,ud,null!==n.clazz.currentHistoricCoverage))}}function qR(e,t){if(1&e&&(Z(0),y(1,"div"),b(2),v(),y(3,"div",7),b(4),v(),Q()),2&e){const n=_(2);f(1),jt("currenthistory ",n.getClassName(n.clazz.methodCoverage,n.clazz.currentHistoricCoverage.mcq),""),f(1),z(" ",n.clazz.methodCoveragePercentage," "),f(1),g("title",n.clazz.currentHistoricCoverage.et+": "+n.clazz.currentHistoricCoverage.methodCoverageRatioText),f(1),z("",n.clazz.currentHistoricCoverage.mcq,"%")}}function WR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),z(" ",n.clazz.methodCoveragePercentage," ")}}function ZR(e,t){if(1&e&&(y(0,"td",9),C(1,GR,1,5,"div",15),C(2,qR,5,6,"ng-container",1),C(3,WR,2,1,"ng-container",1),v()),2&e){const n=_();g("title",n.clazz.methodCoverageRatioText),f(1),g("ngIf",n.clazz.methodCoverageHistory.length>1),f(1),g("ngIf",null!==n.clazz.currentHistoricCoverage),f(1),g("ngIf",null===n.clazz.currentHistoricCoverage)}}function QR(e,t){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),v()),2&e){const n=_();f(1),g("percentage",n.clazz.methodCoverage)}}function YR(e,t){if(1&e&&(y(0,"td",6),b(1),v()),2&e){const n=t.$implicit,r=_();f(1),x(r.clazz.metrics[n.abbreviation])}}let KR=(()=>{var e;class t{constructor(){this.translations={},this.lineCoverageAvailable=!1,this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.visibleMetrics=[],this.historyComparisionDate=""}getClassName(r,o){return r>o?"lightgreen":r<o?"lightred":"lightgraybg"}}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275cmp=Zt({type:e,selectors:[["","class-row",""]],inputs:{clazz:"clazz",translations:"translations",lineCoverageAvailable:"lineCoverageAvailable",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",visibleMetrics:"visibleMetrics",historyComparisionDate:"historyComparisionDate"},attrs:lR,decls:18,vars:17,consts:[[3,"href",4,"ngIf"],[4,"ngIf"],["class","right",4,"ngIf"],["class","right",3,"title",4,"ngIf"],["class","right",4,"ngFor","ngForOf"],[3,"href"],[1,"right"],[3,"title"],[1,"currenthistory"],[1,"right",3,"title"],["coverage-history-chart","","class","tinylinecoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinylinecoveragechart","ct-chart",3,"historicCoverages","ngClass","title"],[3,"percentage"],["coverage-history-chart","","class","tinybranchcoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinybranchcoveragechart","ct-chart",3,"historicCoverages","ngClass","title"],["coverage-history-chart","","class","tinymethodcoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinymethodcoveragechart","ct-chart",3,"historicCoverages","ngClass","title"]],template:function(r,o){1&r&&(y(0,"td"),C(1,cR,2,2,"a",0),C(2,uR,2,1,"ng-container",1),v(),C(3,hR,3,2,"td",2),C(4,mR,3,2,"td",2),C(5,yR,3,2,"td",2),C(6,wR,3,2,"td",2),C(7,MR,4,4,"td",3),C(8,SR,2,1,"td",2),C(9,NR,3,2,"td",2),C(10,RR,3,2,"td",2),C(11,LR,4,4,"td",3),C(12,VR,2,1,"td",2),C(13,jR,3,2,"td",2),C(14,zR,3,2,"td",2),C(15,ZR,4,4,"td",3),C(16,QR,2,1,"td",2),C(17,YR,2,1,"td",4)),2&r&&(f(1),g("ngIf",""!==o.clazz.reportPath),f(1),g("ngIf",""===o.clazz.reportPath),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.lineCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.branchCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngIf",o.methodCoverageAvailable),f(1),g("ngForOf",o.visibleMetrics))},dependencies:[Yo,zr,Zn,aR,hC],encapsulation:2,changeDetection:0}),t})();function JR(e,t){if(1&e){const n=De();y(0,"popup",28),F("visibleChange",function(o){return G(n),q(_(2).popupVisible=o)})("showLineCoverageChange",function(o){return G(n),q(_(2).settings.showLineCoverage=o)})("showBranchCoverageChange",function(o){return G(n),q(_(2).settings.showBranchCoverage=o)})("showMethodCoverageChange",function(o){return G(n),q(_(2).settings.showMethodCoverage=o)})("visibleMetricsChange",function(o){return G(n),q(_(2).settings.visibleMetrics=o)}),v()}if(2&e){const n=_(2);g("visible",n.popupVisible)("translations",n.translations)("branchCoverageAvailable",n.branchCoverageAvailable)("methodCoverageAvailable",n.methodCoverageAvailable)("metrics",n.metrics)("showLineCoverage",n.settings.showLineCoverage)("showBranchCoverage",n.settings.showBranchCoverage)("showMethodCoverage",n.settings.showMethodCoverage)("visibleMetrics",n.settings.visibleMetrics)}}function XR(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),x(n.translations.noGrouping)}}function eF(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),x(n.translations.byAssembly)}}function tF(e,t){if(1&e&&(Z(0),b(1),Q()),2&e){const n=_(2);f(1),x(n.translations.byNamespace+" "+n.settings.grouping)}}function nF(e,t){if(1&e&&(y(0,"option",32),b(1),v()),2&e){const n=t.$implicit;g("value",n),f(1),x(n)}}function rF(e,t){1&e&&N(0,"br")}function oF(e,t){if(1&e&&(y(0,"option",40),b(1),v()),2&e){const n=_(4);f(1),z(" ",n.translations.branchCoverageIncreaseOnly," ")}}function iF(e,t){if(1&e&&(y(0,"option",41),b(1),v()),2&e){const n=_(4);f(1),z(" ",n.translations.branchCoverageDecreaseOnly," ")}}function sF(e,t){if(1&e&&(y(0,"option",42),b(1),v()),2&e){const n=_(4);f(1),z(" ",n.translations.methodCoverageIncreaseOnly," ")}}function aF(e,t){if(1&e&&(y(0,"option",43),b(1),v()),2&e){const n=_(4);f(1),z(" ",n.translations.methodCoverageDecreaseOnly," ")}}function lF(e,t){if(1&e){const n=De();y(0,"div")(1,"select",29),F("ngModelChange",function(o){return G(n),q(_(3).settings.historyComparisionType=o)}),y(2,"option",30),b(3),v(),y(4,"option",33),b(5),v(),y(6,"option",34),b(7),v(),y(8,"option",35),b(9),v(),C(10,oF,2,1,"option",36),C(11,iF,2,1,"option",37),C(12,sF,2,1,"option",38),C(13,aF,2,1,"option",39),v()()}if(2&e){const n=_(3);f(1),g("ngModel",n.settings.historyComparisionType),f(2),x(n.translations.filter),f(2),x(n.translations.allChanges),f(2),x(n.translations.lineCoverageIncreaseOnly),f(2),x(n.translations.lineCoverageDecreaseOnly),f(1),g("ngIf",n.branchCoverageAvailable),f(1),g("ngIf",n.branchCoverageAvailable),f(1),g("ngIf",n.methodCoverageAvailable),f(1),g("ngIf",n.methodCoverageAvailable)}}function cF(e,t){if(1&e){const n=De();Z(0),y(1,"div"),b(2),y(3,"select",29),F("ngModelChange",function(o){return G(n),q(_(2).settings.historyComparisionDate=o)})("ngModelChange",function(){return G(n),q(_(2).updateCurrentHistoricCoverage())}),y(4,"option",30),b(5),v(),C(6,nF,2,2,"option",31),v()(),C(7,rF,1,0,"br",0),C(8,lF,14,9,"div",0),Q()}if(2&e){const n=_(2);f(2),z(" ",n.translations.compareHistory," "),f(1),g("ngModel",n.settings.historyComparisionDate),f(2),x(n.translations.date),f(1),g("ngForOf",n.historicCoverageExecutionTimes),f(1),g("ngIf",""!==n.settings.historyComparisionDate),f(1),g("ngIf",""!==n.settings.historyComparisionDate)}}function uF(e,t){1&e&&N(0,"col",44)}function dF(e,t){1&e&&N(0,"col",45)}function fF(e,t){1&e&&N(0,"col",46)}function hF(e,t){1&e&&N(0,"col",47)}function pF(e,t){1&e&&N(0,"col",48)}function gF(e,t){1&e&&N(0,"col",49)}function mF(e,t){1&e&&N(0,"col",44)}function _F(e,t){1&e&&N(0,"col",47)}function vF(e,t){1&e&&N(0,"col",48)}function yF(e,t){1&e&&N(0,"col",49)}function CF(e,t){1&e&&N(0,"col",44)}function DF(e,t){1&e&&N(0,"col",47)}function wF(e,t){1&e&&N(0,"col",48)}function bF(e,t){1&e&&N(0,"col",49)}function EF(e,t){1&e&&N(0,"col",49)}function IF(e,t){if(1&e&&(y(0,"th",50),b(1),v()),2&e){const n=_(2);f(1),x(n.translations.coverage)}}function MF(e,t){if(1&e&&(y(0,"th",51),b(1),v()),2&e){const n=_(2);f(1),x(n.translations.branchCoverage)}}function SF(e,t){if(1&e&&(y(0,"th",51),b(1),v()),2&e){const n=_(2);f(1),x(n.translations.methodCoverage)}}function AF(e,t){if(1&e&&(y(0,"th",4),b(1),v()),2&e){const n=_(2);It("colspan",n.settings.visibleMetrics.length),f(1),x(n.translations.metrics)}}const dt=function(e,t,n){return{"icon-up-dir_active":e,"icon-down-dir_active":t,"icon-down-dir":n}};function TF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("covered",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"covered"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"covered"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"covered"!==n.settings.sortBy)),f(1),x(n.translations.covered)}}function NF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("uncovered",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"uncovered"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"uncovered"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"uncovered"!==n.settings.sortBy)),f(1),x(n.translations.uncovered)}}function xF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("coverable",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"coverable"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"coverable"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"coverable"!==n.settings.sortBy)),f(1),x(n.translations.coverable)}}function OF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("total",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"total"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"total"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"total"!==n.settings.sortBy)),f(1),x(n.translations.total)}}function RF(e,t){if(1&e){const n=De();y(0,"th",52)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("coverage",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"coverage"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"coverage"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"coverage"!==n.settings.sortBy)),f(1),x(n.translations.percentage)}}function FF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("covered_branches",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"covered_branches"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"covered_branches"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"covered_branches"!==n.settings.sortBy)),f(1),x(n.translations.covered)}}function PF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("total_branches",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"total_branches"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"total_branches"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"total_branches"!==n.settings.sortBy)),f(1),x(n.translations.total)}}function kF(e,t){if(1&e){const n=De();y(0,"th",52)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("branchcoverage",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"branchcoverage"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"branchcoverage"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"branchcoverage"!==n.settings.sortBy)),f(1),x(n.translations.percentage)}}function LF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("covered_methods",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"covered_methods"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"covered_methods"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"covered_methods"!==n.settings.sortBy)),f(1),x(n.translations.covered)}}function VF(e,t){if(1&e){const n=De();y(0,"th",6)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("total_methods",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"total_methods"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"total_methods"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"total_methods"!==n.settings.sortBy)),f(1),x(n.translations.total)}}function HF(e,t){if(1&e){const n=De();y(0,"th",52)(1,"a",3),F("click",function(o){return G(n),q(_(2).updateSorting("methodcoverage",o))}),N(2,"i",24),b(3),v()()}if(2&e){const n=_(2);f(2),g("ngClass",xe(2,dt,"methodcoverage"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"methodcoverage"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"methodcoverage"!==n.settings.sortBy)),f(1),x(n.translations.percentage)}}function BF(e,t){if(1&e){const n=De();y(0,"th")(1,"a",3),F("click",function(o){const s=G(n).$implicit;return q(_(2).updateSorting(s.abbreviation,o))}),N(2,"i",24),b(3),v(),y(4,"a",53),N(5,"i",54),v()()}if(2&e){const n=t.$implicit,r=_(2);f(2),g("ngClass",xe(3,dt,r.settings.sortBy===n.abbreviation&&"desc"===r.settings.sortOrder,r.settings.sortBy===n.abbreviation&&"asc"===r.settings.sortOrder,r.settings.sortBy!==n.abbreviation)),f(1),x(n.name),f(1),bn("href",n.explanationUrl,wn)}}function jF(e,t){if(1&e&&N(0,"tr",56),2&e){const n=_().$implicit,r=_(2);g("element",n)("collapsed",n.collapsed)("lineCoverageAvailable",r.settings.showLineCoverage)("branchCoverageAvailable",r.branchCoverageAvailable&&r.settings.showBranchCoverage)("methodCoverageAvailable",r.methodCoverageAvailable&&r.settings.showMethodCoverage)("visibleMetrics",r.settings.visibleMetrics)}}function $F(e,t){if(1&e&&N(0,"tr",58),2&e){const n=_().$implicit,r=_(3);g("clazz",n)("translations",r.translations)("lineCoverageAvailable",r.settings.showLineCoverage)("branchCoverageAvailable",r.branchCoverageAvailable&&r.settings.showBranchCoverage)("methodCoverageAvailable",r.methodCoverageAvailable&&r.settings.showMethodCoverage)("visibleMetrics",r.settings.visibleMetrics)("historyComparisionDate",r.settings.historyComparisionDate)}}function UF(e,t){if(1&e&&(Z(0),C(1,$F,1,7,"tr",57),Q()),2&e){const n=t.$implicit,r=_().$implicit,o=_(2);f(1),g("ngIf",!r.collapsed&&n.visible(o.settings.filter,o.settings.historyComparisionType))}}function zF(e,t){if(1&e&&N(0,"tr",61),2&e){const n=_().$implicit,r=_(5);g("clazz",n)("translations",r.translations)("lineCoverageAvailable",r.settings.showLineCoverage)("branchCoverageAvailable",r.branchCoverageAvailable&&r.settings.showBranchCoverage)("methodCoverageAvailable",r.methodCoverageAvailable&&r.settings.showMethodCoverage)("visibleMetrics",r.settings.visibleMetrics)("historyComparisionDate",r.settings.historyComparisionDate)}}function GF(e,t){if(1&e&&(Z(0),C(1,zF,1,7,"tr",60),Q()),2&e){const n=t.$implicit,r=_(2).$implicit,o=_(3);f(1),g("ngIf",!r.collapsed&&n.visible(o.settings.filter,o.settings.historyComparisionType))}}function qF(e,t){if(1&e&&(Z(0),N(1,"tr",59),C(2,GF,2,1,"ng-container",27),Q()),2&e){const n=_().$implicit,r=_(3);f(1),g("element",n)("collapsed",n.collapsed)("lineCoverageAvailable",r.settings.showLineCoverage)("branchCoverageAvailable",r.branchCoverageAvailable&&r.settings.showBranchCoverage)("methodCoverageAvailable",r.methodCoverageAvailable&&r.settings.showMethodCoverage)("visibleMetrics",r.settings.visibleMetrics),f(1),g("ngForOf",n.classes)}}function WF(e,t){if(1&e&&(Z(0),C(1,qF,3,7,"ng-container",0),Q()),2&e){const n=t.$implicit,r=_().$implicit,o=_(2);f(1),g("ngIf",!r.collapsed&&n.visible(o.settings.filter,o.settings.historyComparisionType))}}function ZF(e,t){if(1&e&&(Z(0),C(1,jF,1,6,"tr",55),C(2,UF,2,1,"ng-container",27),C(3,WF,2,1,"ng-container",27),Q()),2&e){const n=t.$implicit,r=_(2);f(1),g("ngIf",n.visible(r.settings.filter,r.settings.historyComparisionType)),f(1),g("ngForOf",n.classes),f(1),g("ngForOf",n.subElements)}}function QF(e,t){if(1&e){const n=De();y(0,"div"),C(1,JR,1,9,"popup",1),y(2,"div",2)(3,"div")(4,"a",3),F("click",function(o){return G(n),q(_().collapseAll(o))}),b(5),v(),b(6," | "),y(7,"a",3),F("click",function(o){return G(n),q(_().expandAll(o))}),b(8),v()(),y(9,"div",4),C(10,XR,2,1,"ng-container",0),C(11,eF,2,1,"ng-container",0),C(12,tF,2,1,"ng-container",0),N(13,"br"),b(14),y(15,"input",5),F("ngModelChange",function(o){return G(n),q(_().settings.grouping=o)})("ngModelChange",function(){return G(n),q(_().updateCoverageInfo())}),v()(),y(16,"div",4),C(17,cF,9,6,"ng-container",0),v(),y(18,"div",6)(19,"div")(20,"button",7),F("click",function(){return G(n),q(_().popupVisible=!0)}),N(21,"i",8),b(22),v()(),N(23,"br"),y(24,"div")(25,"span"),b(26),v(),y(27,"input",9),F("ngModelChange",function(o){return G(n),q(_().settings.filter=o)}),v()()()(),y(28,"div",10)(29,"table",11)(30,"colgroup"),N(31,"col",12),C(32,uF,1,0,"col",13),C(33,dF,1,0,"col",14),C(34,fF,1,0,"col",15),C(35,hF,1,0,"col",16),C(36,pF,1,0,"col",17),C(37,gF,1,0,"col",18),C(38,mF,1,0,"col",13),C(39,_F,1,0,"col",16),C(40,vF,1,0,"col",17),C(41,yF,1,0,"col",18),C(42,CF,1,0,"col",13),C(43,DF,1,0,"col",16),C(44,wF,1,0,"col",17),C(45,bF,1,0,"col",18),C(46,EF,1,0,"col",19),v(),y(47,"thead")(48,"tr",20),N(49,"th"),C(50,IF,2,1,"th",21),C(51,MF,2,1,"th",22),C(52,SF,2,1,"th",22),C(53,AF,2,2,"th",23),v(),y(54,"tr")(55,"th")(56,"a",3),F("click",function(o){return G(n),q(_().updateSorting("name",o))}),N(57,"i",24),b(58),v()(),C(59,TF,4,6,"th",25),C(60,NF,4,6,"th",25),C(61,xF,4,6,"th",25),C(62,OF,4,6,"th",25),C(63,RF,4,6,"th",26),C(64,FF,4,6,"th",25),C(65,PF,4,6,"th",25),C(66,kF,4,6,"th",26),C(67,LF,4,6,"th",25),C(68,VF,4,6,"th",25),C(69,HF,4,6,"th",26),C(70,BF,6,7,"th",27),v()(),y(71,"tbody"),C(72,ZF,4,3,"ng-container",27),v()()()()}if(2&e){const n=_();f(1),g("ngIf",n.popupVisible),f(4),x(n.translations.collapseAll),f(3),x(n.translations.expandAll),f(2),g("ngIf",-1===n.settings.grouping),f(1),g("ngIf",0===n.settings.grouping),f(1),g("ngIf",n.settings.grouping>0),f(2),z(" ",n.translations.grouping," "),f(1),g("max",n.settings.groupingMaximum)("ngModel",n.settings.grouping),f(2),g("ngIf",n.historicCoverageExecutionTimes.length>0),f(5),x(n.metrics.length>0?n.translations.selectCoverageTypesAndMetrics:n.translations.selectCoverageTypes),f(4),z("",n.translations.filter," "),f(1),g("ngModel",n.settings.filter),f(5),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngForOf",n.settings.visibleMetrics),f(4),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.settings.visibleMetrics.length>0),f(4),g("ngClass",xe(47,dt,"name"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"name"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"name"!==n.settings.sortBy)),f(1),x(n.translations.name),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.settings.showLineCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.branchCoverageAvailable&&n.settings.showBranchCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngIf",n.methodCoverageAvailable&&n.settings.showMethodCoverage),f(1),g("ngForOf",n.settings.visibleMetrics),f(2),g("ngForOf",n.codeElements)}}let YF=(()=>{var e;class t{constructor(r){this.queryString="",this.historicCoverageExecutionTimes=[],this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.metrics=[],this.codeElements=[],this.translations={},this.popupVisible=!1,this.settings=new AO,this.window=r.nativeWindow}ngOnInit(){this.historicCoverageExecutionTimes=this.window.historicCoverageExecutionTimes,this.branchCoverageAvailable=this.window.branchCoverageAvailable,this.methodCoverageAvailable=this.window.methodCoverageAvailable,this.metrics=this.window.metrics,this.translations=this.window.translations,vt.maximumDecimalPlacesForCoverageQuotas=this.window.maximumDecimalPlacesForCoverageQuotas;let r=!1;if(void 0!==this.window.history&&void 0!==this.window.history.replaceState&&null!==this.window.history.state&&null!=this.window.history.state.coverageInfoSettings)console.log("Coverage info: Restoring from history",this.window.history.state.coverageInfoSettings),r=!0,this.settings=JSON.parse(JSON.stringify(this.window.history.state.coverageInfoSettings));else{let i=0,s=this.window.assemblies;for(let a=0;a<s.length;a++)for(let l=0;l<s[a].classes.length;l++)i=Math.max(i,(s[a].classes[l].name.match(/\.|\\/g)||[]).length);this.settings.groupingMaximum=i,console.log("Grouping maximum: "+i),this.settings.showBranchCoverage=this.branchCoverageAvailable,this.settings.showMethodCoverage=this.methodCoverageAvailable}const o=window.location.href.indexOf("?");o>-1&&(this.queryString=window.location.href.substring(o)),this.updateCoverageInfo(),r&&this.restoreCollapseState()}onBeforeUnload(){if(this.saveCollapseState(),void 0!==this.window.history&&void 0!==this.window.history.replaceState){console.log("Coverage info: Updating history",this.settings);let r=new dC;null!==window.history.state&&(r=JSON.parse(JSON.stringify(this.window.history.state))),r.coverageInfoSettings=JSON.parse(JSON.stringify(this.settings)),window.history.replaceState(r,"")}}updateCoverageInfo(){let r=(new Date).getTime(),o=this.window.assemblies,i=[],s=0;if(0===this.settings.grouping)for(let c=0;c<o.length;c++){let u=new Nn(o[c].name,null);i.push(u);for(let d=0;d<o[c].classes.length;d++)u.insertClass(new ld(o[c].classes[d],this.queryString),null),s++}else if(-1===this.settings.grouping){let c=new Nn(this.translations.all,null);i.push(c);for(let u=0;u<o.length;u++)for(let d=0;d<o[u].classes.length;d++)c.insertClass(new ld(o[u].classes[d],this.queryString),null),s++}else for(let c=0;c<o.length;c++){let u=new Nn(o[c].name,null);i.push(u);for(let d=0;d<o[c].classes.length;d++)u.insertClass(new ld(o[c].classes[d],this.queryString),this.settings.grouping),s++}let a=-1,l=1;"name"===this.settings.sortBy&&(a="asc"===this.settings.sortOrder?-1:1,l="asc"===this.settings.sortOrder?1:-1),i.sort(function(c,u){return c.name===u.name?0:c.name<u.name?a:l}),Nn.sortCodeElementViewModels(i,this.settings.sortBy,"asc"===this.settings.sortOrder);for(let c=0;c<i.length;c++)i[c].changeSorting(this.settings.sortBy,"asc"===this.settings.sortOrder);this.codeElements=i,console.log(`Processing assemblies finished (Duration: ${(new Date).getTime()-r}ms, Assemblies: ${i.length}, Classes: ${s})`),""!==this.settings.historyComparisionDate&&this.updateCurrentHistoricCoverage()}updateCurrentHistoricCoverage(){let r=(new Date).getTime();for(let o=0;o<this.codeElements.length;o++)this.codeElements[o].updateCurrentHistoricCoverage(this.settings.historyComparisionDate);console.log(`Updating current historic coverage finished (Duration: ${(new Date).getTime()-r}ms)`)}collapseAll(r){r.preventDefault();for(let o=0;o<this.codeElements.length;o++)this.codeElements[o].collapse()}expandAll(r){r.preventDefault();for(let o=0;o<this.codeElements.length;o++)this.codeElements[o].expand()}updateSorting(r,o){o.preventDefault(),this.settings.sortOrder=r===this.settings.sortBy&&"asc"===this.settings.sortOrder?"desc":"asc",this.settings.sortBy=r,console.log(`Updating sort column: '${this.settings.sortBy}' (${this.settings.sortOrder})`),Nn.sortCodeElementViewModels(this.codeElements,this.settings.sortBy,"asc"===this.settings.sortOrder);for(let i=0;i<this.codeElements.length;i++)this.codeElements[i].changeSorting(this.settings.sortBy,"asc"===this.settings.sortOrder)}saveCollapseState(){this.settings.collapseStates=[];let r=o=>{for(let i=0;i<o.length;i++)this.settings.collapseStates.push(o[i].collapsed),r(o[i].subElements)};r(this.codeElements)}restoreCollapseState(){let r=0,o=i=>{for(let s=0;s<i.length;s++)this.settings.collapseStates.length>r&&(i[s].collapsed=this.settings.collapseStates[r]),r++,o(i[s].subElements)};o(this.codeElements)}}return(e=t).\u0275fac=function(r){return new(r||e)(M(cd))},e.\u0275cmp=Zt({type:e,selectors:[["coverage-info"]],hostBindings:function(r,o){1&r&&F("beforeunload",function(){return o.onBeforeUnload()},0,cc)},decls:1,vars:1,consts:[[4,"ngIf"],[3,"visible","translations","branchCoverageAvailable","methodCoverageAvailable","metrics","showLineCoverage","showBranchCoverage","showMethodCoverage","visibleMetrics","visibleChange","showLineCoverageChange","showBranchCoverageChange","showMethodCoverageChange","visibleMetricsChange",4,"ngIf"],[1,"customizebox"],["href","#",3,"click"],[1,"center"],["type","range","step","1","min","-1",3,"max","ngModel","ngModelChange"],[1,"right"],["type","button",3,"click"],[1,"icon-cog"],["type","text",3,"ngModel","ngModelChange"],[1,"table-responsive"],[1,"overview","table-fixed","stripped"],[1,"column-min-200"],["class","column90",4,"ngIf"],["class","column105",4,"ngIf"],["class","column100",4,"ngIf"],["class","column70",4,"ngIf"],["class","column98",4,"ngIf"],["class","column112",4,"ngIf"],["class","column112",4,"ngFor","ngForOf"],[1,"header"],["class","center","colspan","6",4,"ngIf"],["class","center","colspan","4",4,"ngIf"],["class","center",4,"ngIf"],[1,"icon-down-dir",3,"ngClass"],["class","right",4,"ngIf"],["class","center","colspan","2",4,"ngIf"],[4,"ngFor","ngForOf"],[3,"visible","translations","branchCoverageAvailable","methodCoverageAvailable","metrics","showLineCoverage","showBranchCoverage","showMethodCoverage","visibleMetrics","visibleChange","showLineCoverageChange","showBranchCoverageChange","showMethodCoverageChange","visibleMetricsChange"],[3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["value","allChanges"],["value","lineCoverageIncreaseOnly"],["value","lineCoverageDecreaseOnly"],["value","branchCoverageIncreaseOnly",4,"ngIf"],["value","branchCoverageDecreaseOnly",4,"ngIf"],["value","methodCoverageIncreaseOnly",4,"ngIf"],["value","methodCoverageDecreaseOnly",4,"ngIf"],["value","branchCoverageIncreaseOnly"],["value","branchCoverageDecreaseOnly"],["value","methodCoverageIncreaseOnly"],["value","methodCoverageDecreaseOnly"],[1,"column90"],[1,"column105"],[1,"column100"],[1,"column70"],[1,"column98"],[1,"column112"],["colspan","6",1,"center"],["colspan","4",1,"center"],["colspan","2",1,"center"],["target","_blank",3,"href"],[1,"icon-info-circled"],["codeelement-row","",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics",4,"ngIf"],["codeelement-row","",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics"],["class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics","historyComparisionDate",4,"ngIf"],["class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics","historyComparisionDate"],["codeelement-row","",1,"namespace",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics"],["class","namespace","class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics","historyComparisionDate",4,"ngIf"],["class-row","",1,"namespace",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","visibleMetrics","historyComparisionDate"]],template:function(r,o){1&r&&C(0,QF,73,51,"div",0),2&r&&g("ngIf",o.codeElements.length>0)},dependencies:[Yo,zr,Zn,od,sd,Xo,ed,ii,oa,oi,VO,iR,KR],encapsulation:2}),t})();class KF{constructor(){this.assembly="",this.numberOfRiskHotspots=10,this.filter="",this.sortBy="",this.sortOrder="asc"}}function JF(e,t){if(1&e&&(y(0,"option",15),b(1),v()),2&e){const n=t.$implicit;g("value",n),f(1),x(n)}}function XF(e,t){if(1&e&&(y(0,"span"),b(1),v()),2&e){const n=_(2);f(1),x(n.translations.top)}}function eP(e,t){1&e&&(y(0,"option",22),b(1,"20"),v())}function tP(e,t){1&e&&(y(0,"option",23),b(1,"50"),v())}function nP(e,t){1&e&&(y(0,"option",24),b(1,"100"),v())}function rP(e,t){if(1&e&&(y(0,"option",15),b(1),v()),2&e){const n=_(3);g("value",n.totalNumberOfRiskHotspots),f(1),x(n.translations.all)}}function oP(e,t){if(1&e){const n=De();y(0,"select",16),F("ngModelChange",function(o){return G(n),q(_(2).settings.numberOfRiskHotspots=o)}),y(1,"option",17),b(2,"10"),v(),C(3,eP,2,0,"option",18),C(4,tP,2,0,"option",19),C(5,nP,2,0,"option",20),C(6,rP,2,2,"option",21),v()}if(2&e){const n=_(2);g("ngModel",n.settings.numberOfRiskHotspots),f(3),g("ngIf",n.totalNumberOfRiskHotspots>10),f(1),g("ngIf",n.totalNumberOfRiskHotspots>20),f(1),g("ngIf",n.totalNumberOfRiskHotspots>50),f(1),g("ngIf",n.totalNumberOfRiskHotspots>100)}}function iP(e,t){1&e&&N(0,"col",25)}const ha=function(e,t,n){return{"icon-up-dir_active":e,"icon-down-dir_active":t,"icon-down-dir":n}};function sP(e,t){if(1&e){const n=De();y(0,"th")(1,"a",12),F("click",function(o){const s=G(n).index;return q(_(2).updateSorting(""+s,o))}),N(2,"i",13),b(3),v(),y(4,"a",26),N(5,"i",27),v()()}if(2&e){const n=t.$implicit,r=t.index,o=_(2);f(2),g("ngClass",xe(3,ha,o.settings.sortBy===""+r&&"desc"===o.settings.sortOrder,o.settings.sortBy===""+r&&"asc"===o.settings.sortOrder,o.settings.sortBy!==""+r)),f(1),x(n.name),f(1),bn("href",n.explanationUrl,wn)}}const aP=function(e,t){return{lightred:e,lightgreen:t}};function lP(e,t){if(1&e&&(y(0,"td",31),b(1),v()),2&e){const n=t.$implicit;g("ngClass",Uc(2,aP,n.exceeded,!n.exceeded)),f(1),x(n.value)}}function cP(e,t){if(1&e&&(y(0,"tr")(1,"td"),b(2),v(),y(3,"td")(4,"a",28),b(5),v()(),y(6,"td",29)(7,"a",28),b(8),v()(),C(9,lP,2,5,"td",30),v()),2&e){const n=t.$implicit,r=_(2);f(2),x(n.assembly),f(2),g("href",n.reportPath+r.queryString,wn),f(1),x(n.class),f(1),g("title",n.methodName),f(1),g("href",n.reportPath+r.queryString+"#file"+n.fileIndex+"_line"+n.line,wn),f(1),z(" ",n.methodShortName," "),f(1),g("ngForOf",n.metrics)}}function uP(e,t){if(1&e){const n=De();y(0,"div")(1,"div",1)(2,"div")(3,"select",2),F("ngModelChange",function(o){return G(n),q(_().settings.assembly=o)})("ngModelChange",function(){return G(n),q(_().updateRiskHotpots())}),y(4,"option",3),b(5),v(),C(6,JF,2,2,"option",4),v()(),y(7,"div",5),C(8,XF,2,1,"span",0),C(9,oP,7,5,"select",6),v(),N(10,"div",5),y(11,"div",7)(12,"span"),b(13),v(),y(14,"input",8),F("ngModelChange",function(o){return G(n),q(_().settings.filter=o)})("ngModelChange",function(){return G(n),q(_().updateRiskHotpots())}),v()()(),y(15,"div",9)(16,"table",10)(17,"colgroup"),N(18,"col")(19,"col")(20,"col"),C(21,iP,1,0,"col",11),v(),y(22,"thead")(23,"tr")(24,"th")(25,"a",12),F("click",function(o){return G(n),q(_().updateSorting("assembly",o))}),N(26,"i",13),b(27),v()(),y(28,"th")(29,"a",12),F("click",function(o){return G(n),q(_().updateSorting("class",o))}),N(30,"i",13),b(31),v()(),y(32,"th")(33,"a",12),F("click",function(o){return G(n),q(_().updateSorting("method",o))}),N(34,"i",13),b(35),v()(),C(36,sP,6,7,"th",14),v()(),y(37,"tbody"),C(38,cP,10,7,"tr",14),function h_(e,t){const n=ee();let r;const o=e+W;n.firstCreatePass?(r=function YM(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];const i=r.factory||(r.factory=kn(r.type)),a=Ke(M);try{const l=Oi(!1),c=i();return Oi(l),function T0(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,E(),o,c),c}finally{Ke(a)}}(39,"slice"),v()()()()}if(2&e){const n=_();f(3),g("ngModel",n.settings.assembly),f(2),x(n.translations.assembly),f(1),g("ngForOf",n.assemblies),f(2),g("ngIf",n.totalNumberOfRiskHotspots>10),f(1),g("ngIf",n.totalNumberOfRiskHotspots>10),f(4),z("",n.translations.filter," "),f(1),g("ngModel",n.settings.filter),f(7),g("ngForOf",n.riskHotspotMetrics),f(5),g("ngClass",xe(20,ha,"assembly"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"assembly"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"assembly"!==n.settings.sortBy)),f(1),x(n.translations.assembly),f(3),g("ngClass",xe(24,ha,"class"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"class"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"class"!==n.settings.sortBy)),f(1),x(n.translations.class),f(3),g("ngClass",xe(28,ha,"method"===n.settings.sortBy&&"desc"===n.settings.sortOrder,"method"===n.settings.sortBy&&"asc"===n.settings.sortOrder,"method"!==n.settings.sortBy)),f(1),x(n.translations.method),f(1),g("ngForOf",n.riskHotspotMetrics),f(2),g("ngForOf",p_(39,16,n.riskHotspots,0,n.settings.numberOfRiskHotspots))}}let dP=(()=>{var e;class t{constructor(r){this.queryString="",this.riskHotspotMetrics=[],this.riskHotspots=[],this.totalNumberOfRiskHotspots=0,this.assemblies=[],this.translations={},this.settings=new KF,this.window=r.nativeWindow}ngOnInit(){this.riskHotspotMetrics=this.window.riskHotspotMetrics,this.translations=this.window.translations,void 0!==this.window.history&&void 0!==this.window.history.replaceState&&null!==this.window.history.state&&null!=this.window.history.state.riskHotspotsSettings&&(console.log("Risk hotspots: Restoring from history",this.window.history.state.riskHotspotsSettings),this.settings=JSON.parse(JSON.stringify(this.window.history.state.riskHotspotsSettings)));const r=window.location.href.indexOf("?");r>-1&&(this.queryString=window.location.href.substring(r)),this.updateRiskHotpots()}onDonBeforeUnlodad(){if(void 0!==this.window.history&&void 0!==this.window.history.replaceState){console.log("Risk hotspots: Updating history",this.settings);let r=new dC;null!==window.history.state&&(r=JSON.parse(JSON.stringify(this.window.history.state))),r.riskHotspotsSettings=JSON.parse(JSON.stringify(this.settings)),window.history.replaceState(r,"")}}updateRiskHotpots(){const r=this.window.riskHotspots;if(this.totalNumberOfRiskHotspots=r.length,0===this.assemblies.length){let a=[];for(let l=0;l<r.length;l++)-1===a.indexOf(r[l].assembly)&&a.push(r[l].assembly);this.assemblies=a.sort()}let o=[];for(let a=0;a<r.length;a++)""!==this.settings.filter&&-1===r[a].class.toLowerCase().indexOf(this.settings.filter.toLowerCase())||""!==this.settings.assembly&&r[a].assembly!==this.settings.assembly||o.push(r[a]);let i="asc"===this.settings.sortOrder?-1:1,s="asc"===this.settings.sortOrder?1:-1;if("assembly"===this.settings.sortBy)o.sort(function(a,l){return a.assembly===l.assembly?0:a.assembly<l.assembly?i:s});else if("class"===this.settings.sortBy)o.sort(function(a,l){return a.class===l.class?0:a.class<l.class?i:s});else if("method"===this.settings.sortBy)o.sort(function(a,l){return a.methodShortName===l.methodShortName?0:a.methodShortName<l.methodShortName?i:s});else if(""!==this.settings.sortBy){let a=parseInt(this.settings.sortBy,10);o.sort(function(l,c){return l.metrics[a].value===c.metrics[a].value?0:l.metrics[a].value<c.metrics[a].value?i:s})}this.riskHotspots=o}updateSorting(r,o){o.preventDefault(),this.settings.sortOrder=r===this.settings.sortBy&&"asc"===this.settings.sortOrder?"desc":"asc",this.settings.sortBy=r,console.log(`Updating sort column: '${this.settings.sortBy}' (${this.settings.sortOrder})`),this.updateRiskHotpots()}}return(e=t).\u0275fac=function(r){return new(r||e)(M(cd))},e.\u0275cmp=Zt({type:e,selectors:[["risk-hotspots"]],hostBindings:function(r,o){1&r&&F("beforeunload",function(){return o.onDonBeforeUnlodad()},0,cc)},decls:1,vars:1,consts:[[4,"ngIf"],[1,"customizebox"],["name","assembly",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"center"],[3,"ngModel","ngModelChange",4,"ngIf"],[1,"right"],["type","text",3,"ngModel","ngModelChange"],[1,"table-responsive"],[1,"overview","table-fixed","stripped"],["class","column105",4,"ngFor","ngForOf"],["href","#",3,"click"],[1,"icon-down-dir",3,"ngClass"],[4,"ngFor","ngForOf"],[3,"value"],[3,"ngModel","ngModelChange"],["value","10"],["value","20",4,"ngIf"],["value","50",4,"ngIf"],["value","100",4,"ngIf"],[3,"value",4,"ngIf"],["value","20"],["value","50"],["value","100"],[1,"column105"],["target","_blank",3,"href"],[1,"icon-info-circled"],[3,"href"],[3,"title"],["class","right",3,"ngClass",4,"ngFor","ngForOf"],[1,"right",3,"ngClass"]],template:function(r,o){1&r&&C(0,uP,40,32,"div",0),2&r&&g("ngIf",o.totalNumberOfRiskHotspots>0)},dependencies:[Yo,zr,Zn,od,sd,Xo,ii,oa,oi,jv],encapsulation:2}),t})(),fP=(()=>{var e;class t{}return(e=t).\u0275fac=function(r){return new(r||e)},e.\u0275mod=pn({type:e,bootstrap:[dP,YF]}),e.\u0275inj=Gt({providers:[cd],imports:[px,SO]}),t})();fx().bootstrapModule(fP).catch(e=>console.error(e))}},_e=>{_e(_e.s=538)}]);