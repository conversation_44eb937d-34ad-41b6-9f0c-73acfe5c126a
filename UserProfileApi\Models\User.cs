using System.ComponentModel.DataAnnotations;

namespace UserProfileApi.Models
{
    public class User
    {
        public long Id { get; set; }

        [Required]
        public string Fullname { get; set; } = "";

        [Required]
        public string Email { get; set; } = "";

        public string? Phone { get; set; }

        public string? Address { get; set; }

        public string? ImageUrl { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
