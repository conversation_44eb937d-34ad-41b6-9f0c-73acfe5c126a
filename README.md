# UserProfileApi

This is a C# ASP.NET Core Web API project for managing user profiles. It includes:
- Basic CRUD operations for user information
- A separate xUnit test project (`UserProfileApi.Tests`) for service logic and code coverage

## Structure
- `Controllers/UsersController.cs`: API endpoints for user operations
- `DTOs/UserDto.cs`: Data transfer object for user info
- `Models/User.cs`: User entity
- `Services/UserService.cs`: Business logic for user management
- `Program.cs`: Entry point
- `UserProfileApi.csproj`: Project file

## Test Project
- `UserServiceTests.cs`: Unit tests for user service
- `UserProfileApi.Tests.csproj`: Test project file

## Build & Run
```bash
# Build the solution
 dotnet build UserProfileApi.sln

# Run the API
 dotnet run --project UserProfileApi/UserProfileApi.csproj

# Run tests with coverage
 dotnet test UserProfileApi.Tests/UserProfileApi.Tests.csproj --collect:"XPlat Code Coverage"
```

Coverage results will be in the `TestResults` folder.
