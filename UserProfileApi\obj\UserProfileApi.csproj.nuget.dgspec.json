{"format": 1, "restore": {"D:\\ASEM5\\SWT\\TestingC\\UserProfileApi\\UserProfileApi.csproj": {}}, "projects": {"D:\\ASEM5\\SWT\\TestingC\\UserProfileApi\\UserProfileApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\ASEM5\\SWT\\TestingC\\UserProfileApi\\UserProfileApi.csproj", "projectName": "UserProfileApi", "projectPath": "D:\\ASEM5\\SWT\\TestingC\\UserProfileApi\\UserProfileApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\ASEM5\\SWT\\TestingC\\UserProfileApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.410\\RuntimeIdentifierGraph.json"}}}}}