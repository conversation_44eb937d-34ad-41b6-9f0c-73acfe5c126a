using System.Security.Claims;
using UserProfileApi.Exceptions;

namespace UserProfileApi.Services
{
    public class AuthUtil : IAuthUtil
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthUtil(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public long GetCurrentUserId()
        {
            // Mock implementation - in real app, get from JW<PERSON> token or session
            // For testing purposes, return a fixed user ID
            var context = _httpContextAccessor.HttpContext;
            
            // Try to get user ID from header (for testing)
            if (context?.Request.Headers.TryGetValue("X-User-Id", out var userIdHeader) == true)
            {
                if (long.TryParse(userIdHeader.FirstOrDefault(), out var userId))
                    return userId;
            }

            // Default to user ID 1 for testing
            return 1;
        }
    }
}
