using UserProfileApi.Models;
using UserProfileApi.DTOs;

namespace UserProfileApi.Services
{
    public interface IUserService
    {
        Task<UserDto> UpdateUserProfileAsync(UserDto request);
        Task<UserDto> GetUserProfileAsync();
        Task<UserDto> UpdateAvatarAsync(IFormFile file);
        Task<User?> GetUserAsync(long id);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User> CreateUserAsync(UserDto dto);
    }
}
