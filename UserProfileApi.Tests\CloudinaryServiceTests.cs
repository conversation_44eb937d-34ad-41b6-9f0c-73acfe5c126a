using Xunit;
using Moq;
using Microsoft.AspNetCore.Http;
using UserProfileApi.Services;
using UserProfileApi.Exceptions;
using System.Text;

namespace UserProfileApi.Tests
{
    public class CloudinaryServiceTests
    {
        private readonly CloudinaryService _cloudinaryService;

        public CloudinaryServiceTests()
        {
            _cloudinaryService = new CloudinaryService();
        }

        [Fact]
        public async Task UploadImageAsync_ValidJpegFile_ReturnsImageUrl()
        {
            // Arrange
            var mockFile = CreateMockFormFile("test.jpg", "image/jpeg", 1024);

            // Act
            var result = await _cloudinaryService.UploadImageAsync(mockFile);

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith("https://res.cloudinary.com/demo/image/upload/v1/", result);
            Assert.EndsWith(".jpg", result);
        }

        [Fact]
        public async Task UploadImageAsync_ValidPngFile_ReturnsImageUrl()
        {
            // Arrange
            var mockFile = CreateMockFormFile("test.png", "image/png", 2048);

            // Act
            var result = await _cloudinaryService.UploadImageAsync(mockFile);

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith("https://res.cloudinary.com/demo/image/upload/v1/", result);
            Assert.EndsWith(".png", result);
        }

        [Fact]
        public async Task UploadImageAsync_NullFile_ThrowsAppException()
        {
            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _cloudinaryService.UploadImageAsync(null!));
            Assert.Equal("File không hợp lệ", exception.Message);
        }

        [Fact]
        public async Task UploadImageAsync_EmptyFile_ThrowsAppException()
        {
            // Arrange
            var mockFile = CreateMockFormFile("empty.jpg", "image/jpeg", 0);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _cloudinaryService.UploadImageAsync(mockFile));
            Assert.Equal("File không hợp lệ", exception.Message);
        }

        [Theory]
        [InlineData("application/pdf")]
        [InlineData("text/plain")]
        [InlineData("video/mp4")]
        [InlineData("application/json")]
        public async Task UploadImageAsync_InvalidFileType_ThrowsAppException(string contentType)
        {
            // Arrange
            var mockFile = CreateMockFormFile("test.pdf", contentType, 1024);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _cloudinaryService.UploadImageAsync(mockFile));
            Assert.Equal("Chỉ chấp nhận file ảnh (JPEG, PNG, GIF)", exception.Message);
        }

        [Fact]
        public async Task UploadImageAsync_FileTooLarge_ThrowsAppException()
        {
            // Arrange - Create a file larger than 5MB
            var mockFile = CreateMockFormFile("large.jpg", "image/jpeg", 6 * 1024 * 1024);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _cloudinaryService.UploadImageAsync(mockFile));
            Assert.Equal("File ảnh không được vượt quá 5MB", exception.Message);
        }

        [Theory]
        [InlineData("image/jpeg")]
        [InlineData("image/jpg")]
        [InlineData("image/png")]
        [InlineData("image/gif")]
        public async Task UploadImageAsync_AllowedFileTypes_ReturnsImageUrl(string contentType)
        {
            // Arrange
            var extension = contentType.Split('/')[1];
            if (extension == "jpeg") extension = "jpg";
            
            var mockFile = CreateMockFormFile($"test.{extension}", contentType, 1024);

            // Act
            var result = await _cloudinaryService.UploadImageAsync(mockFile);

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith("https://res.cloudinary.com/demo/image/upload/v1/", result);
        }

        [Fact]
        public async Task UploadImageAsync_MaxAllowedSize_ReturnsImageUrl()
        {
            // Arrange - Create a file exactly 5MB
            var mockFile = CreateMockFormFile("max.jpg", "image/jpeg", 5 * 1024 * 1024);

            // Act
            var result = await _cloudinaryService.UploadImageAsync(mockFile);

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith("https://res.cloudinary.com/demo/image/upload/v1/", result);
        }

        // Helper method to create mock IFormFile
        private static IFormFile CreateMockFormFile(string fileName, string contentType, long length)
        {
            var content = new byte[length];
            var stream = new MemoryStream(content);
            
            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns(fileName);
            mockFile.Setup(f => f.ContentType).Returns(contentType);
            mockFile.Setup(f => f.Length).Returns(length);
            mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
            mockFile.Setup(f => f.CopyToAsync(It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                .Returns((Stream target, CancellationToken token) => stream.CopyToAsync(target, token));

            return mockFile.Object;
        }
    }
}
