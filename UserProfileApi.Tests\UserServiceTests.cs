using Xunit;
using Moq;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using UserProfileApi.Services;
using UserProfileApi.Repositories;
using UserProfileApi.Models;
using UserProfileApi.DTOs;
using UserProfileApi.Exceptions;
using UserProfileApi.Mappings;
using System.Text;

namespace UserProfileApi.Tests
{
    public class UserServiceTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IAuthUtil> _mockAuthUtil;
        private readonly Mock<ICloudinaryService> _mockCloudinaryService;
        private readonly IMapper _mapper;
        private readonly UserService _userService;

        public UserServiceTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockAuthUtil = new Mock<IAuthUtil>();
            _mockCloudinaryService = new Mock<ICloudinaryService>();

            // Setup AutoMapper
            var config = new MapperConfiguration(cfg => cfg.AddProfile<UserProfile>());
            _mapper = config.CreateMapper();

            _userService = new UserService(
                _mockUserRepository.Object,
                _mockAuthUtil.Object,
                _mapper,
                _mockCloudinaryService.Object
            );
        }

        [Fact]
        public async Task GetUserProfileAsync_UserExists_ReturnsUserDto()
        {
            // Arrange
            var userId = 1L;
            var user = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>",
                Phone = "0123456789",
                Address = "Test Address"
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(user);

            // Act
            var result = await _userService.GetUserProfileAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.Fullname, result.Fullname);
            Assert.Equal(user.Email, result.Email);
            Assert.Equal(user.Phone, result.Phone);
            Assert.Equal(user.Address, result.Address);
        }

        [Fact]
        public async Task GetUserProfileAsync_UserNotExists_ThrowsAppException()
        {
            // Arrange
            var userId = 1L;
            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync((User?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _userService.GetUserProfileAsync());
            Assert.Equal("Người dùng không tồn tại", exception.Message);
        }

        [Fact]
        public async Task UpdateUserProfileAsync_ValidRequest_ReturnsUpdatedUserDto()
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Old Name",
                Email = "<EMAIL>",
                Phone = "0000000000",
                Address = "Old Address"
            };

            var updateRequest = new UserDto
            {
                Fullname = "New Name",
                Phone = "1111111111",
                Address = "New Address"
            };

            var updatedUser = new User
            {
                Id = userId,
                Fullname = "New Name",
                Email = "<EMAIL>",
                Phone = "1111111111",
                Address = "New Address"
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockUserRepository.Setup(x => x.SaveAsync(It.IsAny<User>())).ReturnsAsync(updatedUser);

            // Act
            var result = await _userService.UpdateUserProfileAsync(updateRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("New Name", result.Fullname);
            Assert.Equal("1111111111", result.Phone);
            Assert.Equal("New Address", result.Address);
            _mockUserRepository.Verify(x => x.SaveAsync(It.IsAny<User>()), Times.Once);
        }

        [Fact]
        public async Task UpdateUserProfileAsync_UserNotExists_ThrowsAppException()
        {
            // Arrange
            var userId = 1L;
            var updateRequest = new UserDto { Fullname = "New Name" };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync((User?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _userService.UpdateUserProfileAsync(updateRequest));
            Assert.Equal("Người dùng không tồn tại", exception.Message);
        }

        [Fact]
        public async Task UpdateUserProfileAsync_WithImageUpload_UploadsImageAndUpdatesUser()
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            var mockFile = CreateMockFormFile("test.jpg", "image/jpeg");
            var updateRequest = new UserDto
            {
                Fullname = "Updated Name",
                Img = mockFile
            };

            var imageUrl = "https://cloudinary.com/test-image.jpg";
            var updatedUser = new User
            {
                Id = userId,
                Fullname = "Updated Name",
                Email = "<EMAIL>",
                ImageUrl = imageUrl
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockCloudinaryService.Setup(x => x.UploadImageAsync(mockFile)).ReturnsAsync(imageUrl);
            _mockUserRepository.Setup(x => x.SaveAsync(It.IsAny<User>())).ReturnsAsync(updatedUser);

            // Act
            var result = await _userService.UpdateUserProfileAsync(updateRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated Name", result.Fullname);
            Assert.Equal(imageUrl, result.ImageUrl);
            _mockCloudinaryService.Verify(x => x.UploadImageAsync(mockFile), Times.Once);
            _mockUserRepository.Verify(x => x.SaveAsync(It.IsAny<User>()), Times.Once);
        }

        [Fact]
        public async Task UpdateUserProfileAsync_ImageUploadFails_ThrowsAppException()
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            var mockFile = CreateMockFormFile("test.jpg", "image/jpeg");
            var updateRequest = new UserDto
            {
                Fullname = "Updated Name",
                Img = mockFile
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockCloudinaryService.Setup(x => x.UploadImageAsync(mockFile))
                .ThrowsAsync(new Exception("Upload failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _userService.UpdateUserProfileAsync(updateRequest));
            Assert.Contains("Không thể tải lên hình ảnh", exception.Message);
        }

        [Fact]
        public async Task UpdateAvatarAsync_ValidFile_ReturnsUpdatedUserDto()
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            var mockFile = CreateMockFormFile("avatar.jpg", "image/jpeg");
            var imageUrl = "https://cloudinary.com/avatar.jpg";
            var updatedUser = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>",
                ImageUrl = imageUrl
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockCloudinaryService.Setup(x => x.UploadImageAsync(mockFile)).ReturnsAsync(imageUrl);
            _mockUserRepository.Setup(x => x.SaveAsync(It.IsAny<User>())).ReturnsAsync(updatedUser);

            // Act
            var result = await _userService.UpdateAvatarAsync(mockFile);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(imageUrl, result.ImageUrl);
            _mockCloudinaryService.Verify(x => x.UploadImageAsync(mockFile), Times.Once);
            _mockUserRepository.Verify(x => x.SaveAsync(It.IsAny<User>()), Times.Once);
        }

        [Fact]
        public async Task UpdateAvatarAsync_UserNotExists_ThrowsAppException()
        {
            // Arrange
            var userId = 1L;
            var mockFile = CreateMockFormFile("avatar.jpg", "image/jpeg");

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync((User?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _userService.UpdateAvatarAsync(mockFile));
            Assert.Equal("Người dùng không tồn tại", exception.Message);
        }

        [Fact]
        public async Task UpdateAvatarAsync_UploadFails_ThrowsAppException()
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            var mockFile = CreateMockFormFile("avatar.jpg", "image/jpeg");

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockCloudinaryService.Setup(x => x.UploadImageAsync(mockFile))
                .ThrowsAsync(new Exception("Upload failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AppException>(() => _userService.UpdateAvatarAsync(mockFile));
            Assert.Contains("Không thể tải lên hình ảnh", exception.Message);
        }

        [Fact]
        public async Task GetUserAsync_UserExists_ReturnsUser()
        {
            // Arrange
            var userId = 1L;
            var user = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(user);

            // Act
            var result = await _userService.GetUserAsync(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.Id, result.Id);
            Assert.Equal(user.Fullname, result.Fullname);
            Assert.Equal(user.Email, result.Email);
        }

        [Fact]
        public async Task GetUserAsync_UserNotExists_ReturnsNull()
        {
            // Arrange
            var userId = 1L;
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync((User?)null);

            // Act
            var result = await _userService.GetUserAsync(userId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetAllUsersAsync_ReturnsAllUsers()
        {
            // Arrange
            var users = new List<User>
            {
                new User { Id = 1, Fullname = "User 1", Email = "<EMAIL>" },
                new User { Id = 2, Fullname = "User 2", Email = "<EMAIL>" }
            };

            _mockUserRepository.Setup(x => x.GetAllAsync()).ReturnsAsync(users);

            // Act
            var result = await _userService.GetAllUsersAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Contains(result, u => u.Fullname == "User 1");
            Assert.Contains(result, u => u.Fullname == "User 2");
        }

        [Fact]
        public async Task CreateUserAsync_ValidDto_ReturnsCreatedUser()
        {
            // Arrange
            var userDto = new UserDto
            {
                Fullname = "New User",
                Email = "<EMAIL>",
                Phone = "0123456789",
                Address = "New Address"
            };

            var createdUser = new User
            {
                Id = 4,
                Fullname = "New User",
                Email = "<EMAIL>",
                Phone = "0123456789",
                Address = "New Address"
            };

            _mockUserRepository.Setup(x => x.SaveAsync(It.IsAny<User>())).ReturnsAsync(createdUser);

            // Act
            var result = await _userService.CreateUserAsync(userDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(createdUser.Id, result.Id);
            Assert.Equal(createdUser.Fullname, result.Fullname);
            Assert.Equal(createdUser.Email, result.Email);
            Assert.Equal(createdUser.Phone, result.Phone);
            Assert.Equal(createdUser.Address, result.Address);
            _mockUserRepository.Verify(x => x.SaveAsync(It.IsAny<User>()), Times.Once);
        }

        [Theory]
        [InlineData(null, "<EMAIL>", "0123456789", "Test Address")]
        [InlineData("", "<EMAIL>", "0123456789", "Test Address")]
        [InlineData("Test User", null, "0123456789", "Test Address")]
        [InlineData("Test User", "", "0123456789", "Test Address")]
        public async Task UpdateUserProfileAsync_PartialUpdate_OnlyUpdatesProvidedFields(
            string? fullname, string? email, string? phone, string? address)
        {
            // Arrange
            var userId = 1L;
            var existingUser = new User
            {
                Id = userId,
                Fullname = "Original Name",
                Email = "<EMAIL>",
                Phone = "0000000000",
                Address = "Original Address"
            };

            var updateRequest = new UserDto
            {
                Fullname = fullname,
                Phone = phone,
                Address = address
            };

            _mockAuthUtil.Setup(x => x.GetCurrentUserId()).Returns(userId);
            _mockUserRepository.Setup(x => x.FindByIdAsync(userId)).ReturnsAsync(existingUser);
            _mockUserRepository.Setup(x => x.SaveAsync(It.IsAny<User>())).ReturnsAsync(existingUser);

            // Act
            var result = await _userService.UpdateUserProfileAsync(updateRequest);

            // Assert
            Assert.NotNull(result);

            // Verify that only non-null/non-empty fields were updated
            if (!string.IsNullOrEmpty(fullname))
                Assert.Equal(fullname, existingUser.Fullname);
            else
                Assert.Equal("Original Name", existingUser.Fullname);

            if (!string.IsNullOrEmpty(phone))
                Assert.Equal(phone, existingUser.Phone);
            else
                Assert.Equal("0000000000", existingUser.Phone);

            if (!string.IsNullOrEmpty(address))
                Assert.Equal(address, existingUser.Address);
            else
                Assert.Equal("Original Address", existingUser.Address);
        }

        // Helper method to create mock IFormFile
        private static IFormFile CreateMockFormFile(string fileName, string contentType)
        {
            var content = "fake image content";
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));

            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns(fileName);
            mockFile.Setup(f => f.ContentType).Returns(contentType);
            mockFile.Setup(f => f.Length).Returns(stream.Length);
            mockFile.Setup(f => f.OpenReadStream()).Returns(stream);
            mockFile.Setup(f => f.CopyToAsync(It.IsAny<Stream>(), It.IsAny<CancellationToken>()))
                .Returns((Stream target, CancellationToken token) => stream.CopyToAsync(target, token));

            return mockFile.Object;
        }
    }
}