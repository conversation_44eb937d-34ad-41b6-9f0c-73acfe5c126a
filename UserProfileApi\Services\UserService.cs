using UserProfileApi.Models;
using UserProfileApi.DTOs;

namespace UserProfileApi.Services
{
    public class UserService
    {
        private readonly Dictionary<int, User> _users = new();

        public UserService()
        {
            // Thêm dữ liệu cứng
            SeedData();
        }

        private void SeedData()
        {
            _users[1] = new User { Id = 1, Name = "Nguyễn Văn A", Email = "<EMAIL>" };
            _users[2] = new User { Id = 2, Name = "Trần Thị B", Email = "<EMAIL>" };
            _users[3] = new User { Id = 3, Name = "Lê Văn C", Email = "<EMAIL>" };
            _users[4] = new User { Id = 4, Name = "Phạm Thị D", Email = "<EMAIL>" };
            _users[5] = new User { Id = 5, Name = "Hoàng Văn E", Email = "<EMAIL>" };
        }

        public User? GetUser(int id) => _users.TryGetValue(id, out var user) ? user : null;

        public IEnumerable<User> GetAllUsers() => _users.Values;

        public User UpdateUser(int id, UserDto dto)
        {
            if (!_users.ContainsKey(id)) throw new KeyNotFoundException();
            var user = _users[id];
            user.Name = dto.Name;
            user.Email = dto.Email;
            return user;
        }

        public User CreateUser(UserDto dto)
        {
            int newId = _users.Keys.Count > 0 ? _users.Keys.Max() + 1 : 1;
            var user = new User { Id = newId, Name = dto.Name, Email = dto.Email };
            _users[newId] = user;
            return user;
        }
    }
}
