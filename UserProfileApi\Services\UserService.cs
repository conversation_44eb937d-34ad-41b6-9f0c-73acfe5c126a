using UserProfileApi.Models;
using UserProfileApi.DTOs;
using UserProfileApi.Repositories;
using UserProfileApi.Exceptions;
using AutoMapper;

namespace UserProfileApi.Services
{
    public class UserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IAuthUtil _authUtil;
        private readonly IMapper _mapper;
        private readonly ICloudinaryService _cloudinaryService;

        public UserService(
            IUserRepository userRepository,
            IAuthUtil authUtil,
            IMapper mapper,
            ICloudinaryService cloudinaryService)
        {
            _userRepository = userRepository;
            _authUtil = authUtil;
            _mapper = mapper;
            _cloudinaryService = cloudinaryService;
        }

        public async Task<UserDto> UpdateUserProfileAsync(UserDto request)
        {
            var userId = _authUtil.GetCurrentUserId();
            var user = await _userRepository.FindByIdAsync(userId);

            if (user == null)
                throw new AppException("Người dùng không tồn tại");

            // Handle image upload if provided
            if (request.Img != null)
            {
                try
                {
                    var imageUrl = await _cloudinaryService.UploadImageAsync(request.Img);
                    request.ImageUrl = imageUrl;
                }
                catch (Exception e)
                {
                    throw new AppException($"Không thể tải lên hình ảnh: {e.Message}");
                }
            }

            // Update user properties if provided
            if (!string.IsNullOrEmpty(request.Fullname)) user.Fullname = request.Fullname;
            if (!string.IsNullOrEmpty(request.Phone)) user.Phone = request.Phone;
            if (!string.IsNullOrEmpty(request.Address)) user.Address = request.Address;
            if (!string.IsNullOrEmpty(request.ImageUrl)) user.ImageUrl = request.ImageUrl;
            if (request.DateOfBirth.HasValue) user.DateOfBirth = request.DateOfBirth;

            var updated = await _userRepository.SaveAsync(user);
            return _mapper.Map<UserDto>(updated);
        }

        public async Task<UserDto> GetUserProfileAsync()
        {
            var userId = _authUtil.GetCurrentUserId();
            var user = await _userRepository.FindByIdAsync(userId);

            if (user == null)
                throw new AppException("Người dùng không tồn tại");

            return _mapper.Map<UserDto>(user);
        }

        public async Task<UserDto> UpdateAvatarAsync(IFormFile file)
        {
            var userId = _authUtil.GetCurrentUserId();
            var user = await _userRepository.FindByIdAsync(userId);

            if (user == null)
                throw new AppException("Người dùng không tồn tại");

            try
            {
                var imageUrl = await _cloudinaryService.UploadImageAsync(file);
                user.ImageUrl = imageUrl;
                var updated = await _userRepository.SaveAsync(user);
                return _mapper.Map<UserDto>(updated);
            }
            catch (Exception e)
            {
                throw new AppException($"Không thể tải lên hình ảnh: {e.Message}");
            }
        }

        // Additional methods for testing
        public async Task<User?> GetUserAsync(long id) => await _userRepository.FindByIdAsync(id);

        public async Task<IEnumerable<User>> GetAllUsersAsync() => await _userRepository.GetAllAsync();

        public async Task<User> CreateUserAsync(UserDto dto)
        {
            var user = _mapper.Map<User>(dto);
            return await _userRepository.SaveAsync(user);
        }
    }
}
