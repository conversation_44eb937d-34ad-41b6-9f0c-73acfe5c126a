﻿<?xml version="1.0" encoding="utf-8"?>
<coverage line-rate="0.6779000000000001" branch-rate="0.7954000000000001" version="1.9" timestamp="1753467839" lines-covered="160" lines-valid="236" branches-covered="35" branches-valid="44">
  <sources>
    <source>D:\ASEM5\SWT\TestingC\UserProfileApi\</source>
  </sources>
  <packages>
    <package name="UserProfileApi" line-rate="0.6779000000000001" branch-rate="0.7954000000000001" complexity="78">
      <classes>
        <class name="Program" filename="Program.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="&lt;Main&gt;$" signature="(System.String[])" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="4" hits="0" branch="False" />
                <line number="7" hits="0" branch="False" />
                <line number="10" hits="0" branch="False" />
                <line number="11" hits="0" branch="False" />
                <line number="14" hits="0" branch="False" />
                <line number="17" hits="0" branch="False" />
                <line number="20" hits="0" branch="False" />
                <line number="21" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="28" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="155" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="29" hits="0" branch="False" />
                <line number="30" hits="0" branch="False" />
                <line number="31" hits="0" branch="False" />
                <line number="32" hits="0" branch="False" />
                <line number="34" hits="0" branch="False" />
                <line number="36" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
                <line number="40" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="False" />
            <line number="7" hits="0" branch="False" />
            <line number="10" hits="0" branch="False" />
            <line number="11" hits="0" branch="False" />
            <line number="14" hits="0" branch="False" />
            <line number="17" hits="0" branch="False" />
            <line number="20" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="28" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="155" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="29" hits="0" branch="False" />
            <line number="30" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="32" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="40" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.AuthUtil" filename="Services\AuthUtil.cs" line-rate="1" branch-rate="1" complexity="7">
          <methods>
            <method name="GetCurrentUserId" signature="()" line-rate="1" branch-rate="1" complexity="6">
              <lines>
                <line number="16" hits="13" branch="False" />
                <line number="19" hits="13" branch="False" />
                <line number="22" hits="13" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="14" type="jump" coverage="100%" />
                    <condition number="44" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="23" hits="11" branch="False" />
                <line number="24" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="69" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="25" hits="6" branch="False" />
                <line number="26" hits="5" branch="False" />
                <line number="29" hits="7" branch="False" />
                <line number="30" hits="13" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.AspNetCore.Http.IHttpContextAccessor)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="10" hits="13" branch="False" />
                <line number="11" hits="13" branch="False" />
                <line number="12" hits="13" branch="False" />
                <line number="13" hits="13" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="16" hits="13" branch="False" />
            <line number="19" hits="13" branch="False" />
            <line number="22" hits="13" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="14" type="jump" coverage="100%" />
                <condition number="44" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="23" hits="11" branch="False" />
            <line number="24" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="69" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="25" hits="6" branch="False" />
            <line number="26" hits="5" branch="False" />
            <line number="29" hits="7" branch="False" />
            <line number="30" hits="13" branch="False" />
            <line number="10" hits="13" branch="False" />
            <line number="11" hits="13" branch="False" />
            <line number="12" hits="13" branch="False" />
            <line number="13" hits="13" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.CloudinaryService/&lt;UploadImageAsync&gt;d__0" filename="Services\CloudinaryService.cs" line-rate="1" branch-rate="1" complexity="8">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="8">
              <lines>
                <line number="9" hits="14" branch="False" />
                <line number="10" hits="14" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="24" type="jump" coverage="100%" />
                    <condition number="46" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="11" hits="2" branch="False" />
                <line number="14" hits="12" branch="False" />
                <line number="15" hits="12" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="135" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="16" hits="4" branch="False" />
                <line number="19" hits="8" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="171" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="20" hits="1" branch="False" />
                <line number="23" hits="7" branch="False" />
                <line number="26" hits="7" branch="False" />
                <line number="27" hits="7" branch="False" />
                <line number="28" hits="7" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="9" hits="14" branch="False" />
            <line number="10" hits="14" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="24" type="jump" coverage="100%" />
                <condition number="46" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="11" hits="2" branch="False" />
            <line number="14" hits="12" branch="False" />
            <line number="15" hits="12" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="135" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="16" hits="4" branch="False" />
            <line number="19" hits="8" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="171" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="20" hits="1" branch="False" />
            <line number="23" hits="7" branch="False" />
            <line number="26" hits="7" branch="False" />
            <line number="27" hits="7" branch="False" />
            <line number="28" hits="7" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name=".ctor" signature="(UserProfileApi.Repositories.IUserRepository,UserProfileApi.Services.IAuthUtil,AutoMapper.IMapper,UserProfileApi.Services.ICloudinaryService)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="17" branch="False" />
                <line number="17" hits="17" branch="False" />
                <line number="18" hits="17" branch="False" />
                <line number="19" hits="17" branch="False" />
                <line number="20" hits="17" branch="False" />
                <line number="21" hits="17" branch="False" />
                <line number="22" hits="17" branch="False" />
                <line number="23" hits="17" branch="False" />
                <line number="24" hits="17" branch="False" />
                <line number="25" hits="17" branch="False" />
                <line number="26" hits="17" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="16" hits="17" branch="False" />
            <line number="17" hits="17" branch="False" />
            <line number="18" hits="17" branch="False" />
            <line number="19" hits="17" branch="False" />
            <line number="20" hits="17" branch="False" />
            <line number="21" hits="17" branch="False" />
            <line number="22" hits="17" branch="False" />
            <line number="23" hits="17" branch="False" />
            <line number="24" hits="17" branch="False" />
            <line number="25" hits="17" branch="False" />
            <line number="26" hits="17" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;CreateUserAsync&gt;d__10" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="99" hits="1" branch="False" />
                <line number="100" hits="1" branch="False" />
                <line number="101" hits="1" branch="False" />
                <line number="102" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="99" hits="1" branch="False" />
            <line number="100" hits="1" branch="False" />
            <line number="101" hits="1" branch="False" />
            <line number="102" hits="1" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;GetAllUsersAsync&gt;d__9" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="96" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="96" hits="1" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;GetUserAsync&gt;d__8" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="94" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="94" hits="2" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;GetUserProfileAsync&gt;d__6" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="62" hits="2" branch="False" />
                <line number="63" hits="2" branch="False" />
                <line number="64" hits="2" branch="False" />
                <line number="66" hits="2" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="186" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="67" hits="1" branch="False" />
                <line number="69" hits="1" branch="False" />
                <line number="70" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="62" hits="2" branch="False" />
            <line number="63" hits="2" branch="False" />
            <line number="64" hits="2" branch="False" />
            <line number="66" hits="2" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="186" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="67" hits="1" branch="False" />
            <line number="69" hits="1" branch="False" />
            <line number="70" hits="1" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;UpdateAvatarAsync&gt;d__7" filename="Services\UserService.cs" line-rate="1" branch-rate="1" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="73" hits="3" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="16" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="74" hits="3" branch="False" />
                <line number="75" hits="3" branch="False" />
                <line number="77" hits="3" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="199" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="78" hits="1" branch="False" />
                <line number="81" hits="2" branch="False" />
                <line number="82" hits="2" branch="False" />
                <line number="83" hits="1" branch="False" />
                <line number="84" hits="1" branch="False" />
                <line number="85" hits="1" branch="False" />
                <line number="87" hits="1" branch="False" />
                <line number="88" hits="1" branch="False" />
                <line number="89" hits="1" branch="False" />
                <line number="91" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="73" hits="3" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="16" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="74" hits="3" branch="False" />
            <line number="75" hits="3" branch="False" />
            <line number="77" hits="3" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="199" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="78" hits="1" branch="False" />
            <line number="81" hits="2" branch="False" />
            <line number="82" hits="2" branch="False" />
            <line number="83" hits="1" branch="False" />
            <line number="84" hits="1" branch="False" />
            <line number="85" hits="1" branch="False" />
            <line number="87" hits="1" branch="False" />
            <line number="88" hits="1" branch="False" />
            <line number="89" hits="1" branch="False" />
            <line number="91" hits="1" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Services.UserService/&lt;UpdateUserProfileAsync&gt;d__5" filename="Services\UserService.cs" line-rate="1" branch-rate="0.9285" complexity="14">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="0.9285" complexity="14">
              <lines>
                <line number="29" hits="8" branch="False" />
                <line number="30" hits="8" branch="False" />
                <line number="31" hits="8" branch="False" />
                <line number="33" hits="8" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="211" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="34" hits="1" branch="False" />
                <line number="37" hits="7" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="242" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="38" hits="2" branch="False" />
                <line number="40" hits="2" branch="False" />
                <line number="41" hits="2" branch="False" />
                <line number="42" hits="1" branch="False" />
                <line number="43" hits="1" branch="False" />
                <line number="44" hits="1" branch="False" />
                <line number="45" hits="1" branch="False" />
                <line number="46" hits="1" branch="False" />
                <line number="48" hits="1" branch="False" />
                <line number="51" hits="10" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="492" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="52" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="540" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="53" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="588" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="54" hits="7" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="636" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="55" hits="6" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="685" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="57" hits="6" branch="False" />
                <line number="58" hits="6" branch="False" />
                <line number="59" hits="6" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="29" hits="8" branch="False" />
            <line number="30" hits="8" branch="False" />
            <line number="31" hits="8" branch="False" />
            <line number="33" hits="8" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="211" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="34" hits="1" branch="False" />
            <line number="37" hits="7" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="242" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="38" hits="2" branch="False" />
            <line number="40" hits="2" branch="False" />
            <line number="41" hits="2" branch="False" />
            <line number="42" hits="1" branch="False" />
            <line number="43" hits="1" branch="False" />
            <line number="44" hits="1" branch="False" />
            <line number="45" hits="1" branch="False" />
            <line number="46" hits="1" branch="False" />
            <line number="48" hits="1" branch="False" />
            <line number="51" hits="10" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="492" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="52" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="540" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="53" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="588" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="54" hits="7" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="636" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="55" hits="6" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="685" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="57" hits="6" branch="False" />
            <line number="58" hits="6" branch="False" />
            <line number="59" hits="6" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Repositories.UserRepository" filename="Repositories\UserRepository.cs" line-rate="0" branch-rate="1" complexity="2">
          <methods>
            <method name="SeedData" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="15" hits="0" branch="False" />
                <line number="16" hits="0" branch="False" />
                <line number="17" hits="0" branch="False" />
                <line number="18" hits="0" branch="False" />
                <line number="19" hits="0" branch="False" />
                <line number="20" hits="0" branch="False" />
                <line number="21" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="24" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="26" hits="0" branch="False" />
                <line number="27" hits="0" branch="False" />
                <line number="28" hits="0" branch="False" />
                <line number="29" hits="0" branch="False" />
                <line number="30" hits="0" branch="False" />
                <line number="31" hits="0" branch="False" />
                <line number="32" hits="0" branch="False" />
                <line number="33" hits="0" branch="False" />
                <line number="34" hits="0" branch="False" />
                <line number="35" hits="0" branch="False" />
                <line number="36" hits="0" branch="False" />
                <line number="37" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="40" hits="0" branch="False" />
                <line number="41" hits="0" branch="False" />
                <line number="42" hits="0" branch="False" />
                <line number="43" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="7" hits="0" branch="False" />
                <line number="9" hits="0" branch="False" />
                <line number="10" hits="0" branch="False" />
                <line number="11" hits="0" branch="False" />
                <line number="12" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="15" hits="0" branch="False" />
            <line number="16" hits="0" branch="False" />
            <line number="17" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="19" hits="0" branch="False" />
            <line number="20" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="24" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="26" hits="0" branch="False" />
            <line number="27" hits="0" branch="False" />
            <line number="28" hits="0" branch="False" />
            <line number="29" hits="0" branch="False" />
            <line number="30" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="32" hits="0" branch="False" />
            <line number="33" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="35" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
            <line number="37" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="40" hits="0" branch="False" />
            <line number="41" hits="0" branch="False" />
            <line number="42" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="7" hits="0" branch="False" />
            <line number="9" hits="0" branch="False" />
            <line number="10" hits="0" branch="False" />
            <line number="11" hits="0" branch="False" />
            <line number="12" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Repositories.UserRepository/&lt;FindByIdAsync&gt;d__3" filename="Repositories\UserRepository.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="46" hits="0" branch="False" />
                <line number="47" hits="0" branch="False" />
                <line number="48" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="140" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="49" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="46" hits="0" branch="False" />
            <line number="47" hits="0" branch="False" />
            <line number="48" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="140" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="49" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Repositories.UserRepository/&lt;GetAllAsync&gt;d__5" filename="Repositories\UserRepository.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="67" hits="0" branch="False" />
                <line number="68" hits="0" branch="False" />
                <line number="69" hits="0" branch="False" />
                <line number="70" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="67" hits="0" branch="False" />
            <line number="68" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="70" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Repositories.UserRepository/&lt;SaveAsync&gt;d__4" filename="Repositories\UserRepository.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="52" hits="0" branch="False" />
                <line number="53" hits="0" branch="False" />
                <line number="54" hits="0" branch="False" />
                <line number="56" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="148" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="57" hits="0" branch="False" />
                <line number="58" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="179" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
                <line number="62" hits="0" branch="False" />
                <line number="63" hits="0" branch="False" />
                <line number="64" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="52" hits="0" branch="False" />
            <line number="53" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="56" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="148" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="57" hits="0" branch="False" />
            <line number="58" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="179" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="62" hits="0" branch="False" />
            <line number="63" hits="0" branch="False" />
            <line number="64" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Models.User" filename="Models\User.cs" line-rate="1" branch-rate="1" complexity="9">
          <methods>
            <method name="get_Id" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="7" hits="37" branch="False" />
              </lines>
            </method>
            <method name="get_Fullname" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="10" hits="70" branch="False" />
              </lines>
            </method>
            <method name="get_Email" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="13" hits="57" branch="False" />
              </lines>
            </method>
            <method name="get_Phone" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="15" hits="29" branch="False" />
              </lines>
            </method>
            <method name="get_Address" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="17" hits="29" branch="False" />
              </lines>
            </method>
            <method name="get_ImageUrl" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="19" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_DateOfBirth" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="8" branch="False" />
              </lines>
            </method>
            <method name="get_CreatedAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="23" hits="22" branch="False" />
              </lines>
            </method>
            <method name="get_UpdatedAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="25" hits="22" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="7" hits="37" branch="False" />
            <line number="10" hits="70" branch="False" />
            <line number="13" hits="57" branch="False" />
            <line number="15" hits="29" branch="False" />
            <line number="17" hits="29" branch="False" />
            <line number="19" hits="12" branch="False" />
            <line number="21" hits="8" branch="False" />
            <line number="23" hits="22" branch="False" />
            <line number="25" hits="22" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Mappings.UserProfile" filename="Mappings\UserProfile.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name=".ctor" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="9" hits="17" branch="False" />
                <line number="10" hits="17" branch="False" />
                <line number="11" hits="17" branch="False" />
                <line number="12" hits="34" branch="False" />
                <line number="14" hits="17" branch="False" />
                <line number="15" hits="17" branch="False" />
                <line number="16" hits="17" branch="False" />
                <line number="17" hits="17" branch="False" />
                <line number="18" hits="176" branch="False" />
                <line number="19" hits="17" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="9" hits="17" branch="False" />
            <line number="10" hits="17" branch="False" />
            <line number="11" hits="17" branch="False" />
            <line number="12" hits="34" branch="False" />
            <line number="14" hits="17" branch="False" />
            <line number="15" hits="17" branch="False" />
            <line number="16" hits="17" branch="False" />
            <line number="17" hits="17" branch="False" />
            <line number="18" hits="176" branch="False" />
            <line number="19" hits="17" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Exceptions.AppException" filename="Exceptions\AppException.cs" line-rate="0.5" branch-rate="1" complexity="2">
          <methods>
            <method name=".ctor" signature="(System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="5" hits="16" branch="False" />
                <line number="6" hits="16" branch="False" />
                <line number="7" hits="16" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="9" hits="0" branch="False" />
                <line number="10" hits="0" branch="False" />
                <line number="11" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="5" hits="16" branch="False" />
            <line number="6" hits="16" branch="False" />
            <line number="7" hits="16" branch="False" />
            <line number="9" hits="0" branch="False" />
            <line number="10" hits="0" branch="False" />
            <line number="11" hits="0" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.DTOs.UserDto" filename="DTOs\UserDto.cs" line-rate="1" branch-rate="1" complexity="8">
          <methods>
            <method name="get_Id" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="5" hits="11" branch="False" />
              </lines>
            </method>
            <method name="get_Fullname" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="6" hits="42" branch="False" />
              </lines>
            </method>
            <method name="get_Email" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="7" hits="16" branch="False" />
              </lines>
            </method>
            <method name="get_Phone" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="8" hits="33" branch="False" />
              </lines>
            </method>
            <method name="get_Address" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="9" hits="28" branch="False" />
              </lines>
            </method>
            <method name="get_ImageUrl" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="10" hits="22" branch="False" />
              </lines>
            </method>
            <method name="get_DateOfBirth" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="15" branch="False" />
              </lines>
            </method>
            <method name="get_Img" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="12" hits="11" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="5" hits="11" branch="False" />
            <line number="6" hits="42" branch="False" />
            <line number="7" hits="16" branch="False" />
            <line number="8" hits="33" branch="False" />
            <line number="9" hits="28" branch="False" />
            <line number="10" hits="22" branch="False" />
            <line number="11" hits="15" branch="False" />
            <line number="12" hits="11" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name=".ctor" signature="(UserProfileApi.Services.IUserService)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="14" hits="11" branch="False" />
                <line number="15" hits="11" branch="False" />
                <line number="16" hits="11" branch="False" />
                <line number="17" hits="11" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="14" hits="11" branch="False" />
            <line number="15" hits="11" branch="False" />
            <line number="16" hits="11" branch="False" />
            <line number="17" hits="11" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;Create&gt;d__7" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="87" hits="2" branch="False" />
                <line number="89" hits="2" branch="False" />
                <line number="90" hits="2" branch="False" />
                <line number="91" hits="1" branch="False" />
                <line number="93" hits="1" branch="False" />
                <line number="94" hits="1" branch="False" />
                <line number="95" hits="1" branch="False" />
                <line number="97" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="87" hits="2" branch="False" />
            <line number="89" hits="2" branch="False" />
            <line number="90" hits="2" branch="False" />
            <line number="91" hits="1" branch="False" />
            <line number="93" hits="1" branch="False" />
            <line number="94" hits="1" branch="False" />
            <line number="95" hits="1" branch="False" />
            <line number="97" hits="2" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;Get&gt;d__6" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="80" hits="2" branch="False" />
                <line number="81" hits="2" branch="False" />
                <line number="82" hits="2" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="157" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="83" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="80" hits="2" branch="False" />
            <line number="81" hits="2" branch="False" />
            <line number="82" hits="2" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="157" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="83" hits="2" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;GetAll&gt;d__5" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="73" hits="1" branch="False" />
                <line number="74" hits="1" branch="False" />
                <line number="75" hits="1" branch="False" />
                <line number="76" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="73" hits="1" branch="False" />
            <line number="74" hits="1" branch="False" />
            <line number="75" hits="1" branch="False" />
            <line number="76" hits="1" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;GetProfile&gt;d__2" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="24" hits="2" branch="False" />
                <line number="26" hits="2" branch="False" />
                <line number="27" hits="2" branch="False" />
                <line number="28" hits="1" branch="False" />
                <line number="30" hits="1" branch="False" />
                <line number="31" hits="1" branch="False" />
                <line number="32" hits="1" branch="False" />
                <line number="34" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="24" hits="2" branch="False" />
            <line number="26" hits="2" branch="False" />
            <line number="27" hits="2" branch="False" />
            <line number="28" hits="1" branch="False" />
            <line number="30" hits="1" branch="False" />
            <line number="31" hits="1" branch="False" />
            <line number="32" hits="1" branch="False" />
            <line number="34" hits="2" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;UpdateAvatar&gt;d__4" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="58" hits="2" branch="False" />
                <line number="60" hits="2" branch="False" />
                <line number="61" hits="2" branch="False" />
                <line number="62" hits="1" branch="False" />
                <line number="64" hits="1" branch="False" />
                <line number="65" hits="1" branch="False" />
                <line number="66" hits="1" branch="False" />
                <line number="68" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="58" hits="2" branch="False" />
            <line number="60" hits="2" branch="False" />
            <line number="61" hits="2" branch="False" />
            <line number="62" hits="1" branch="False" />
            <line number="64" hits="1" branch="False" />
            <line number="65" hits="1" branch="False" />
            <line number="66" hits="1" branch="False" />
            <line number="68" hits="2" branch="False" />
          </lines>
        </class>
        <class name="UserProfileApi.Controllers.UsersController/&lt;UpdateProfile&gt;d__3" filename="Controllers\UsersController.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="41" hits="2" branch="False" />
                <line number="43" hits="2" branch="False" />
                <line number="44" hits="2" branch="False" />
                <line number="45" hits="1" branch="False" />
                <line number="47" hits="1" branch="False" />
                <line number="48" hits="1" branch="False" />
                <line number="49" hits="1" branch="False" />
                <line number="51" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="41" hits="2" branch="False" />
            <line number="43" hits="2" branch="False" />
            <line number="44" hits="2" branch="False" />
            <line number="45" hits="1" branch="False" />
            <line number="47" hits="1" branch="False" />
            <line number="48" hits="1" branch="False" />
            <line number="49" hits="1" branch="False" />
            <line number="51" hits="2" branch="False" />
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>