using Xunit;
using Moq;
using Microsoft.AspNetCore.Mvc;
using UserProfileApi.Controllers;
using UserProfileApi.Services;
using UserProfileApi.DTOs;
using UserProfileApi.Models;
using UserProfileApi.Exceptions;
using Microsoft.AspNetCore.Http;
using System.Text;

namespace UserProfileApi.Tests
{
    public class UsersControllerTests
    {
        private readonly Mock<IUserService> _mockUserService;
        private readonly UsersController _controller;

        public UsersControllerTests()
        {
            _mockUserService = new Mock<IUserService>();
            _controller = new UsersController(_mockUserService.Object);
        }

        [Fact]
        public async Task GetProfile_UserExists_ReturnsOkWithUserDto()
        {
            // Arrange
            var userDto = new UserDto
            {
                Id = 1,
                Fullname = "Test User",
                Email = "<EMAIL>",
                Phone = "0123456789"
            };

            _mockUserService.Setup(s => s.GetUserProfileAsync()).ReturnsAsync(userDto);

            // Act
            var result = await _controller.GetProfile();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedUser = Assert.IsType<UserDto>(okResult.Value);
            Assert.Equal(userDto.Fullname, returnedUser.Fullname);
            Assert.Equal(userDto.Email, returnedUser.Email);
        }

        [Fact]
        public async Task GetProfile_UserNotExists_ReturnsBadRequest()
        {
            // Arrange
            _mockUserService.Setup(s => s.GetUserProfileAsync())
                .ThrowsAsync(new AppException("Người dùng không tồn tại"));

            // Act
            var result = await _controller.GetProfile();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errorResponse = badRequestResult.Value;
            Assert.NotNull(errorResponse);
        }

        [Fact]
        public async Task UpdateProfile_ValidRequest_ReturnsOkWithUpdatedUser()
        {
            // Arrange
            var updateDto = new UserDto
            {
                Fullname = "Updated Name",
                Phone = "0987654321"
            };

            var updatedUser = new UserDto
            {
                Id = 1,
                Fullname = "Updated Name",
                Email = "<EMAIL>",
                Phone = "0987654321"
            };

            _mockUserService.Setup(s => s.UpdateUserProfileAsync(updateDto)).ReturnsAsync(updatedUser);

            // Act
            var result = await _controller.UpdateProfile(updateDto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedUser = Assert.IsType<UserDto>(okResult.Value);
            Assert.Equal(updatedUser.Fullname, returnedUser.Fullname);
            Assert.Equal(updatedUser.Phone, returnedUser.Phone);
        }

        [Fact]
        public async Task UpdateProfile_ServiceThrowsException_ReturnsBadRequest()
        {
            // Arrange
            var updateDto = new UserDto { Fullname = "Updated Name" };
            _mockUserService.Setup(s => s.UpdateUserProfileAsync(updateDto))
                .ThrowsAsync(new AppException("Update failed"));

            // Act
            var result = await _controller.UpdateProfile(updateDto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task UpdateAvatar_ValidFile_ReturnsOkWithUpdatedUser()
        {
            // Arrange
            var mockFile = CreateMockFormFile("avatar.jpg", "image/jpeg");
            var updatedUser = new UserDto
            {
                Id = 1,
                Fullname = "Test User",
                ImageUrl = "https://cloudinary.com/avatar.jpg"
            };

            _mockUserService.Setup(s => s.UpdateAvatarAsync(mockFile)).ReturnsAsync(updatedUser);

            // Act
            var result = await _controller.UpdateAvatar(mockFile);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedUser = Assert.IsType<UserDto>(okResult.Value);
            Assert.Equal(updatedUser.ImageUrl, returnedUser.ImageUrl);
        }

        [Fact]
        public async Task UpdateAvatar_ServiceThrowsException_ReturnsBadRequest()
        {
            // Arrange
            var mockFile = CreateMockFormFile("avatar.jpg", "image/jpeg");
            _mockUserService.Setup(s => s.UpdateAvatarAsync(mockFile))
                .ThrowsAsync(new AppException("Upload failed"));

            // Act
            var result = await _controller.UpdateAvatar(mockFile);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task GetAll_ReturnsOkWithAllUsers()
        {
            // Arrange
            var users = new List<User>
            {
                new User { Id = 1, Fullname = "User 1", Email = "<EMAIL>" },
                new User { Id = 2, Fullname = "User 2", Email = "<EMAIL>" }
            };

            _mockUserService.Setup(s => s.GetAllUsersAsync()).ReturnsAsync(users);

            // Act
            var result = await _controller.GetAll();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedUsers = Assert.IsAssignableFrom<IEnumerable<User>>(okResult.Value);
            Assert.Equal(2, returnedUsers.Count());
        }

        [Fact]
        public async Task Get_UserExists_ReturnsOkWithUser()
        {
            // Arrange
            var userId = 1L;
            var user = new User
            {
                Id = userId,
                Fullname = "Test User",
                Email = "<EMAIL>"
            };

            _mockUserService.Setup(s => s.GetUserAsync(userId)).ReturnsAsync(user);

            // Act
            var result = await _controller.Get(userId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedUser = Assert.IsType<User>(okResult.Value);
            Assert.Equal(user.Id, returnedUser.Id);
            Assert.Equal(user.Fullname, returnedUser.Fullname);
        }

        [Fact]
        public async Task Get_UserNotExists_ReturnsNotFound()
        {
            // Arrange
            var userId = 999L;
            _mockUserService.Setup(s => s.GetUserAsync(userId)).ReturnsAsync((User?)null);

            // Act
            var result = await _controller.Get(userId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Create_ValidDto_ReturnsCreatedAtAction()
        {
            // Arrange
            var createDto = new UserDto
            {
                Fullname = "New User",
                Email = "<EMAIL>"
            };

            var createdUser = new User
            {
                Id = 4,
                Fullname = "New User",
                Email = "<EMAIL>"
            };

            _mockUserService.Setup(s => s.CreateUserAsync(createDto)).ReturnsAsync(createdUser);

            // Act
            var result = await _controller.Create(createDto);

            // Assert
            var createdResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal(nameof(UsersController.Get), createdResult.ActionName);
            Assert.Equal(createdUser.Id, ((dynamic)createdResult.RouteValues!).id);
            Assert.Equal(createdUser, createdResult.Value);
        }

        [Fact]
        public async Task Create_ServiceThrowsException_ReturnsBadRequest()
        {
            // Arrange
            var createDto = new UserDto { Fullname = "New User" };
            _mockUserService.Setup(s => s.CreateUserAsync(createDto))
                .ThrowsAsync(new AppException("Creation failed"));

            // Act
            var result = await _controller.Create(createDto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        // Helper method to create mock IFormFile
        private static IFormFile CreateMockFormFile(string fileName, string contentType)
        {
            var content = "fake image content";
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(content));
            
            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns(fileName);
            mockFile.Setup(f => f.ContentType).Returns(contentType);
            mockFile.Setup(f => f.Length).Returns(stream.Length);
            mockFile.Setup(f => f.OpenReadStream()).Returns(stream);

            return mockFile.Object;
        }
    }
}
