using Xunit;
using Moq;
using Microsoft.AspNetCore.Http;
using UserProfileApi.Services;
using Microsoft.Extensions.Primitives;

namespace UserProfileApi.Tests
{
    public class AuthUtilTests
    {
        [Fact]
        public void GetCurrentUserId_WithValidUserIdHeader_ReturnsUserId()
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockHeaders = new Mock<IHeaderDictionary>();

            var userId = "123";
            mockHeaders.Setup(h => h.TryGetValue("X-User-Id", out It.Ref<StringValues>.IsAny))
                .Returns((string key, out StringValues value) =>
                {
                    value = new StringValues(userId);
                    return true;
                });

            mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
            mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(123L, result);
        }

        [Fact]
        public void GetCurrentUserId_WithInvalidUserIdHeader_ReturnsDefaultUserId()
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockHeaders = new Mock<IHeaderDictionary>();

            var invalidUserId = "invalid";
            mockHeaders.Setup(h => h.TryGetValue("X-User-Id", out It.Ref<StringValues>.IsAny))
                .Returns((string key, out StringValues value) =>
                {
                    value = new StringValues(invalidUserId);
                    return true;
                });

            mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
            mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(1L, result); // Default user ID
        }

        [Fact]
        public void GetCurrentUserId_WithoutUserIdHeader_ReturnsDefaultUserId()
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockHeaders = new Mock<IHeaderDictionary>();

            mockHeaders.Setup(h => h.TryGetValue("X-User-Id", out It.Ref<StringValues>.IsAny))
                .Returns((string key, out StringValues value) =>
                {
                    value = StringValues.Empty;
                    return false;
                });

            mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
            mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(1L, result); // Default user ID
        }

        [Fact]
        public void GetCurrentUserId_WithNullHttpContext_ReturnsDefaultUserId()
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns((HttpContext?)null);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(1L, result); // Default user ID
        }

        [Theory]
        [InlineData("1", 1L)]
        [InlineData("999", 999L)]
        [InlineData("0", 0L)]
        [InlineData("9223372036854775807", 9223372036854775807L)] // Max long value
        public void GetCurrentUserId_WithVariousValidUserIds_ReturnsCorrectUserId(string headerValue, long expectedUserId)
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockHeaders = new Mock<IHeaderDictionary>();

            mockHeaders.Setup(h => h.TryGetValue("X-User-Id", out It.Ref<StringValues>.IsAny))
                .Returns((string key, out StringValues value) =>
                {
                    value = new StringValues(headerValue);
                    return true;
                });

            mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
            mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(expectedUserId, result);
        }

        [Theory]
        [InlineData("")]
        [InlineData("abc")]
        [InlineData("12.34")]
        [InlineData("-1")]
        [InlineData("9223372036854775808")] // Overflow long
        public void GetCurrentUserId_WithInvalidUserIdValues_ReturnsDefaultUserId(string headerValue)
        {
            // Arrange
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockHeaders = new Mock<IHeaderDictionary>();

            mockHeaders.Setup(h => h.TryGetValue("X-User-Id", out It.Ref<StringValues>.IsAny))
                .Returns((string key, out StringValues value) =>
                {
                    value = new StringValues(headerValue);
                    return true;
                });

            mockRequest.Setup(r => r.Headers).Returns(mockHeaders.Object);
            mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            var authUtil = new AuthUtil(mockHttpContextAccessor.Object);

            // Act
            var result = authUtil.GetCurrentUserId();

            // Assert
            Assert.Equal(1L, result); // Default user ID
        }
    }
}
