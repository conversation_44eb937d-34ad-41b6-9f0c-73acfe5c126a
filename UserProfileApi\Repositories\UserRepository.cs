using UserProfileApi.Models;

namespace UserProfileApi.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly Dictionary<long, User> _users = new();

        public UserRepository()
        {
            SeedData();
        }

        private void SeedData()
        {
            _users[1] = new User 
            { 
                Id = 1, 
                Fullname = "Nguyễn Văn A", 
                Email = "<EMAIL>",
                Phone = "0123456789",
                Address = "Hà Nội",
                DateOfBirth = new DateTime(1990, 1, 1)
            };
            _users[2] = new User 
            { 
                Id = 2, 
                Fullname = "Trần Thị B", 
                Email = "<EMAIL>",
                Phone = "0987654321",
                Address = "<PERSON><PERSON> Chí Minh",
                DateOfBirth = new DateTime(1992, 5, 15)
            };
            _users[3] = new User 
            { 
                Id = 3, 
                Fullname = "Lê Văn C", 
                Email = "<EMAIL>",
                Phone = "0369852147",
                Address = "Đà Nẵng",
                DateOfBirth = new DateTime(1988, 12, 20)
            };
        }

        public async Task<User?> FindByIdAsync(long id)
        {
            await Task.Delay(10); // Simulate async database call
            return _users.TryGetValue(id, out var user) ? user : null;
        }

        public async Task<User> SaveAsync(User user)
        {
            await Task.Delay(10); // Simulate async database call
            user.UpdatedAt = DateTime.UtcNow;
            
            if (user.Id == 0)
            {
                user.Id = _users.Keys.Count > 0 ? _users.Keys.Max() + 1 : 1;
                user.CreatedAt = DateTime.UtcNow;
            }
            
            _users[user.Id] = user;
            return user;
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            await Task.Delay(10); // Simulate async database call
            return _users.Values;
        }
    }
}
